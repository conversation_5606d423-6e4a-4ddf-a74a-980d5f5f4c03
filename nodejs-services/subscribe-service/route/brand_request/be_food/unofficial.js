const moment = require('moment-timezone')
const _ = require('lodash');
const axios = require('axios');
const FormData = require('form-data');
const sharp = require('sharp');
moment.tz.setDefault('Asia/Bangkok');
const { Order, Site, Hub, Brand, BrandMenu, User, UserNotification, SiteFinance, OrderFeedback, SiteFeedback, OrderReport, GoogleSheetFile } = require('../../../../.shared/database')
const { base_headers, base_body } = require('../../../../.shared/merchant/be');
const { get_token_by_site, get_token_by_code } = require('../../../../.shared/token_account');
const { text_slugify, text_compare, retry_operation, compare_arrays, get_file_extension_from_url } = require('../../../../.shared/helper');
const { NTF_BRANDS } = require('../../../../.shared/const');

const sync_brand_menu = async (brand_id) => {
    const brand = await Brand.findById(brand_id);

    let be_code = 'NEXDOR_be'
    if (NTF_BRANDS.includes(brand.name)) {
        be_code = 'NTF_be'
    }
    const master_token = await get_token_by_code(be_code)
    const { sheet_data } = await GoogleSheetFile.findOne({ file_id: brand.menu_sheet_file_id })

    const option_items = sheet_data.MD_option.filter(v => v.brand === brand.name) || []

    await sync_brand_menu_option_items(master_token, option_items)
    const updated_items = sheet_data.MD_menu.filter(v => v.brand === brand.name).map(v => ({
        category_name: v.category,
        name: v.name,
        description: v.description,
        images: [v.image, v.image_1, v.image_2].filter(v => v),
        price: Number(v.price),
        option_ids: v.option_ids ? v.option_ids.split('\n').map(v => v.trim()) : []
    }))

    await sync_master_menu_items(master_token, { updated_items }, option_items)
}

const sync_site_menu = async (site_codes) => {
    const sites = await Site.find({ code: site_codes });
    const brand = await Brand.findById(sites[0].brand_id);
    const { sheet_data } = await GoogleSheetFile.findOne({ file_id: brand.menu_sheet_file_id })
    const be_restaurant_ids = []
    for (const site of sites) {
        const token = await get_token_by_site(site, 'be')
        if (!token?.access_token || !token?.site_id) {
            continue
        }
        be_restaurant_ids.push(JSON.parse(token.site_id).restaurant_id)
    }

    let be_code = 'NEXDOR_be'
    if (NTF_BRANDS.includes(brand.name)) {
        be_code = 'NTF_be'
    }

    const master_token = await get_token_by_code(be_code)
    const updated_items = sheet_data.MD_menu.filter(v => v.brand === brand.name && v.be === 'TRUE').map(v => ({
        category_name: v.category,
        name: v.name,
        description: v.description,
        images: [v.image, v.image_1, v.image_2].filter(v => v),
        price: Number(v.price),
    }))

    try {
        await sync_site_menu_items(be_restaurant_ids, master_token, { updated_items })
    } catch (error) {
        console.log(error)
    }

}

const sync_site_menu_items = async function (be_restaurant_ids, master_token, { updated_items }) {
    const group_menu_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_merchant_items`, base_body(master_token), { headers: base_headers() });
    const store_list_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_stores_of_merchant`, base_body(master_token), { headers: base_headers() });

    const grab_categories = group_menu_resp.data?.restaurant_items || [];

    const grab_items = []
    for (const cat of grab_categories) {
        for (const item of cat.items) {
            grab_items.push({
                category_id: cat.category.category_id,
                category_name: cat.category.name,
                id: item.restaurant_item_id,
                name: item.item_name,
                price: item.price,
                description: item.item_details,
                images: [item.item_image]
            })

        }
    }
    // const assign_items = grab_items.filter(v => updated_items.some(g => text_compare(g.name, v.name) && text_compare(g.category_name, v.category_name)));
    // const unassign_items = grab_items.filter(v => !updated_items.some(g => text_compare(g.name, v.name) && text_compare(g.category_name, v.category_name)));
    let assign_items = []
    let unassign_items = []
    for (const grab_item of grab_items) {
        const updated_item = updated_items.find(v => text_compare(v.name, grab_item.name) && text_compare(v.category_name, grab_item.category_name));
        if (updated_item) {
            assign_items.push(grab_item)
        } else {
            unassign_items.push(grab_item)
        }
    }

    if (assign_items.length > 0) {
        const assisn_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/assign_items`,
            {
                ...base_body(master_token),
                merchant_item_ids: assign_items.map(v => v.id),
                store_ids: be_restaurant_ids
            }, { headers: base_headers() });
        console.log(assisn_resp.data)
    }
    if (unassign_items.length > 0) {
        const unassisn_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/unassign_item`,
            {
                ...base_body(master_token),
                merchant_item_ids: unassign_items.map(v => v.id),
                store_ids: be_restaurant_ids
            }, { headers: base_headers() });
        console.log(unassisn_resp.data)
    }

    return { success: true };
}


/**
 * Synchronizes menu items between the site and Grab platform
 * @param {Object} params - The parameters object
 * @param {string} params.site_id - The site ID
 * @param {string} params.access_token - The access token for authentication
 * @param {Object} data - The data object containing updated items
 * @param {Array} data.updated_items - Array of updated menu items
 * @param {string} data.updated_items[].category_name - The category name of the item
 * @param {string} data.updated_items[].name - The name of the item
 * @param {string} data.updated_items[].description - The description of the item
 * @param {Array} data.updated_items[].images - Array of image URLs for the item
 * @param {number} data.updated_items[].price - The price of the item
 * @returns {Object|null} - Returns an object or null if an error occurs
 */
const sync_master_menu_items = async function ({ site_id, access_token }, { updated_items }, option_items) {
    if (!access_token) {
        return {};
    }
    try {
        const group_menu_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_merchant_items`, {
            ...base_body({ site_id, access_token }),

        }, { headers: base_headers() });
        const grab_categories = group_menu_resp.data?.restaurant_items || [];

        // Prepare data for comparison
        const grabCategories = grab_categories.map(cat => ({
            name: cat.category.name,
            id: cat.category.category_id,
            items: cat.items.map(item => ({
                name: item.item_name,
                id: item.restaurant_item_id,
                price: item.price,
                description: item.item_details,
                images: [item.item_image]
            }))
        }));

        const siteCategories = _.chain(updated_items)
            .groupBy('category_name')
            .map((items, category) => ({
                name: category,
                items: items.map(item => ({
                    name: item.name,
                    price: item.price,
                    description: item.description,
                    images: item.images,
                    option_ids: item.option_ids
                }))
            }))
            .value();


        // Compare categories
        const { onlyInLeft: categoriesToCreate, onlyInRight: categoriesToDelete, inBoth: categoriesToUpdate } =
            compare_arrays(siteCategories, grabCategories, (a, b) => text_compare(a.name, b.name));

        // for (const category of categoriesToDelete) {
        //     await deleteCategory(category, { site_id, access_token });
        // }

        // Process categories
        for (const category of categoriesToCreate) {
            await createCategory(category, { site_id, access_token });
        }


        for (const category of categoriesToUpdate) {
            const grabCategory = grabCategories.find(c => text_compare(c.name, category.name));
            await updateCategory(category, grabCategory, { site_id, access_token });
        }

    } catch (err) {
        console.log(err);
        return null;
    }
};


const uploadImage = async function ({ site_id, access_token }, image_url) {
    if (!image_url) return;

    const upload_token_resp = await axios.get(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/food_external_token`, {
        params: {
            service: 'be-restaurant',
            access_token,
            vendor_id: 0,
            merchant_id: JSON.parse(site_id).merchant_id
        }
    })
    const update_token = upload_token_resp.data.token;

    try {
        const imageBuffer = await retry_operation(async () => {
            const response = await axios.get(image_url, { responseType: 'arraybuffer' });
            return Buffer.from(response.data, 'binary');
        }, 2, 'Error downloading the image');

        const newImageBuff = await sharp(imageBuffer).resize(500, 500).jpeg().toBuffer();

        const form = new FormData();
        form.append('private', 'false');
        form.append('service', 'be-food');
        form.append('sizes_thumbnail', JSON.stringify([
            { width: 480, height: 480 },
            { width: 1080, height: 1080 }
        ]));
        form.append('thumbnail', 'true');
        form.append('instant_resize', 'true');
        form.append('image', newImageBuff, {
            filename: 'imagee.jpg',
            contentType: 'application/octet-stream'
        });


        const resp_upload = await retry_operation(async () => {
            return await axios.post('https://saas.be.com.vn/be-photo/prod/upload', form, {
                headers: {
                    ...form.getHeaders(),
                    'authorization': 'Bearer ' + update_token,
                    'user-agent': 'Dart/3.2 (dart:io)'
                }
            })
        }, 2, 'Error uploading the image'
        );

        return resp_upload.data;
    } catch (error) {
        console.log(image_url);
        console.error('Failed to process image after all retry attempts:', error.message);
    }
    return null;
};

async function createCategory(category, { site_id, access_token }) {
    const resp = await axios({
        method: 'post',
        url: `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/create_merchant_category`,
        headers: base_headers(),
        data: {
            ...base_body({ site_id, access_token }),
            name: category.name
        }
    });

    for (const item of category.items) {
        await createItem(item, resp.data.merchant_category_id, { site_id, access_token });
    }
}

async function deleteCategory(category, { site_id, access_token }) {
    for (const item of category.items) {
        await deleteItem(item, { site_id, access_token });
    }
}

async function updateCategory(siteCategory, grabCategory, { site_id, access_token }) {
    const { onlyInLeft: itemsToCreate, onlyInRight: itemsToDelete, inBoth: itemsToUpdate } =
        compare_arrays(siteCategory.items, grabCategory.items, (a, b) => text_compare(a.name, b.name));

    for (const item of itemsToCreate) {
        await createItem(item, grabCategory.id, { site_id, access_token });
    }

    // for (const item of itemsToDelete) {
    //     await deleteItem(item, { site_id, access_token });
    // }

    for (const item of itemsToUpdate) {
        const grabItem = grabCategory.items.find(i => text_compare(i.name, item.name));
        await updateItem(item, grabItem, grabCategory.id, { site_id, access_token });
    }
}

async function createItem(item, categoryID, { site_id, access_token }) {
    const image_data = await uploadImage({ site_id, access_token }, item.images[0]);
    const {
        original,
        resized_thumbnail_w1080_h1080,
        resized_thumbnail_w480_h480,
        thumbnail
    } = image_data.urls;

    const resp = await axios({
        method: 'post',
        url: `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/create_merchant_item`,
        headers: base_headers(),
        data: {
            ...base_body({ site_id, access_token }),
            merchant_item: {
                category_id: categoryID,
                item_name: item.name,
                item_details: item.description,
                item_image_compressed: thumbnail,
                item_image_compressed_web: resized_thumbnail_w480_h480,
                item_image_compressed_cover: resized_thumbnail_w1080_h1080,
                price: item.price,
                customize_ids: null,
                old_price: item.price, // Assuming item object has an old_price property
                item_image: original
            },
        }
    });

    return resp.data; // Assuming you want to return the response data
}

async function deleteItem(item, { site_id, access_token }) {
    const resp = await axios({
        method: 'post',
        url: `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/update_status_merchant_item`,
        headers: base_headers(),
        data: {
            ...base_body({ site_id, access_token }),
            "restaurant_item_ids": [
                item.id
            ],
            "is_active": 2,
        }
    });
    console.log(resp.data)
}

async function updateItem(siteItem, grabItem, categoryID, { site_id, access_token }) {
    const group_menu_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_merchant_item_customizes`, {
        ...base_body({ site_id, access_token }),
    }, { headers: base_headers() });

    const grab_categories = group_menu_resp.data?.item_customizes || [];
    const customize_ids = []
    for (const option of siteItem.option_ids) {
        const option_id = grab_categories.find(v => text_compare(v.name, option))?.customize_id
        if (option_id) {
            customize_ids.push(option_id)
        }
    }

    const image_data = await uploadImage({ site_id, access_token }, siteItem.images[0]);
    const {
        original,
        resized_thumbnail_w1080_h1080,
        resized_thumbnail_w480_h480,
        thumbnail
    } = image_data.urls;

    const resp = await axios({
        method: 'post',
        url: `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/update_merchant_item`,
        headers: base_headers(),
        data: {
            ...base_body({ site_id, access_token }),
            restaurant_item: {
                restaurant_item_id: grabItem.id,
                restaurant_category_id: null,
                category_id: categoryID,
                offer_text: null,
                customize_count: 0,
                is_active: 1,
                item_name: siteItem.name,
                item_details: siteItem.description,
                item_image: original,
                item_image_compressed: thumbnail,
                item_image_compressed_web: resized_thumbnail_w480_h480,
                item_image_compressed_cover: resized_thumbnail_w1080_h1080,
                price: siteItem.price,
                customize_count: customize_ids.length,
                customize_ids: customize_ids,
                old_price: siteItem.price,
            },
        }
    });
    // {flag: 143, message: 'Response has been sent successfully'}
    if (resp.data.flag != 143) {
        console.log(resp.data)
        console.log(siteItem)
        return {
            success: false,
            message: resp.data.message
        }
    }
    return { success: true }
}

const sync_brand_promotion = async (brand_id) => {
    const brand = await Brand.findById(brand_id);
    const { sheet_data } = await GoogleSheetFile.findOne({ file_id: brand.menu_sheet_file_id })

    let be_code = 'NEXDOR_be'
    if (NTF_BRANDS.includes(brand.name)) {
        be_code = 'NTF_be'
    }

    const token = await get_token_by_code(be_code)
    const updated_items = sheet_data.MD_promotion.filter(v => v.brand === brand.name).map(v => ({
        name: v.name,
        price: Number(v.price),
        sell_price: Number(v.sell_price),
        start_time: moment(v.start_time, 'YYYY/MM/DD').isBefore(moment().add(5, 'minutes')) ? moment().add(5, 'minutes').toISOString() : moment(v.start_time, 'YYYY/MM/DD').toISOString(),
        end_time: moment(v.end_time, 'YYYY/MM/DD').toISOString(),
    }))
    await sync_promotion_items(brand, token, { updated_items })
}


const delete_all_promotion_items = async function ({ site_id, access_token }) {
    if (!access_token) {
        console.error('Access token is missing');
        return null;
    }

    try {
        // Fetch existing campaigns
        const campaigns_resp = await axios.get(`https://api.grab.com/food/merchant/v2/campaigns`, {
            headers: base_headers({ site_id, access_token })
        });
        const campaigns = _.concat(campaigns_resp.data?.campaigns.ongoing || [], campaigns_resp.data?.campaigns.upcoming || []);

        // Delete existing campaigns
        for (const campaign of campaigns) {
            await axios.delete(`https://api.grab.com/food/merchant/v2/campaigns/${campaign.campaignID}`, {
                headers: base_headers({ site_id, access_token }),
            });
        }
    } catch (error) { }
}
/**
 * Synchronizes menu items between the site and Grab platform
 * @param {Object} params - The parameters object
 * @param {string} params.site_id - The site ID
 * @param {string} params.access_token - The access token for authentication
 * @param {Object} data - The data object containing updated items
 * @param {Array} data.updated_items - Array of updated menu items
 * @returns {Object|null} - Returns an object or null if an error occurs
 */

const sync_promotion_items = async function (brand, { site_id, access_token }, { updated_items }) {
    if (!access_token) {
        console.error('Access token is missing');
        return null;
    }

    try {
        // await delete_all_promotion_items({ site_id, access_token })

        const group_menu_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/direct_sale_campaign/get_valid_items`,
            {
                ...base_body({ site_id, access_token }),
                "campaign_info": {
                    "campaign_name": `Khuyến mãi gạch giá ${brand.name}`,
                    "start_date": moment(updated_items[0].start_time).unix(),
                    "end_date": moment(updated_items[0].end_time).unix(),
                    "timings": "",
                    "max_allow_per_day": null
                }
            }, { headers: base_headers() });
        const grab_categories = group_menu_resp.data?.restaurant_items || [];

        const grab_items = []
        for (const cat of grab_categories) {
            for (const item of cat.items) {
                grab_items.push({
                    category_name: cat.category.name,
                    category_id: cat.category.category_id,
                    id: item.restaurant_item_id,
                    name: item.item_name,
                    price: item.price,
                    description: item.item_details,
                    images: [item.item_image]
                })

            }
        }

        const itemNameMap = new Map(grab_items.map(item => [item.name, item]));

        let direct_sale_items = []
        for (const updated_item of updated_items) {
            const grab_item = itemNameMap.get(updated_item.name);
            if (!grab_item) {
                console.warn(`Item "${updated_item.name}" not found in the Grab menu. Skipping campaign creation.`);
                continue;
            }
            direct_sale_items.push({
                "sale_price": updated_item.sell_price,
                "campaign_id": 0,
                "restaurant_item_id": String(grab_item.id),
                "item_image": grab_item.images[0],
                "restaurant_item_name": grab_item.name,
                "old_price": grab_item.price,
                "category_name": grab_item.category_name
            })
        }
        if (direct_sale_items.length === 0) {
            console.log('No items to sync');
            return { success: true }
        }

        direct_sale_items = _.uniqBy(direct_sale_items, 'restaurant_item_id');

        for (const item of direct_sale_items) {
            console.log(`Creating campaign for item "${item.restaurant_item_name}"`);
            const campain_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/direct_sale_campaign/create`, {
                ...base_body({ site_id, access_token }),
                "campaign_info": {
                    "campaign_name": `Gạch giá - ${item.restaurant_item_name}`,
                    "start_date": moment(updated_items[0].start_time).unix(),
                    "end_date": moment(updated_items[0].end_time).unix(),
                    "timings": "",
                    "max_allow_per_day": null,
                    "direct_sale_items": [item]
                },
                "direct_sale_items": [item]
            }, {
                headers: base_headers(),
            });
            console.log(campain_resp.data)
        }

        console.log(`Successfully synced ${direct_sale_items.length} menu items`);
        return { success: true, itemCount: direct_sale_items.length };
    } catch (error) {
        console.error('Error syncing menu items:', error);
        return null;
    }
};

const sync_brand_menu_option_items = async function ({ site_id, access_token }, option_categories) {
    if (!access_token) {
        return {};
    }

    try {

        const group_menu_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_merchant_item_customizes`, {
            ...base_body({ site_id, access_token }),
        }, { headers: base_headers() });

        const grab_categories = group_menu_resp.data?.item_customizes || [];

        // Prepare data for comparison
        const grabCategories = grab_categories.map(cat => ({
            name: cat.name,
            id: cat.customize_id,
            items: cat.customize_options.map(item => ({
                id: item.option_id,
                name: item.name,
                price: item.price,
            }))
        }));

        const siteCategories = _.chain(option_categories)
            .groupBy('option_id')
            .map((items, category) => ({
                name: category,
                items: items.map(item => ({
                    name: item.option,
                    price: item.option_price,
                })),
                min: items[0].min,
                max: items[0].max,
            }))
            .value();

        // Compare categories
        const { onlyInLeft: categoriesToCreate, onlyInRight: categoriesToDelete, inBoth: categoriesToUpdate } =
            compare_arrays(siteCategories, grabCategories, (a, b) => text_compare(a.name, b.name));


        const customizes = grabCategories.filter(v => categoriesToUpdate.some(c => text_compare(c.name, v.name)))
        const group_menu_delete_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/update_status_merchant_item_customize`, {
            ...base_body({ site_id, access_token }),
            "customize_ids": customizes.map(v => v.id),
            "is_active": 2,
        }, { headers: base_headers() });
        console.log(group_menu_delete_resp.data)
        // Process categories
        for (const category of [...categoriesToCreate, ...categoriesToUpdate]) {
            const group_menu_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/create_merchant_item_customize`, {
                ...base_body({ site_id, access_token }),
                "item_customize": {
                    "customize_id": null,
                    "restaurant_item_id": null,
                    "restaurant_id": null,
                    "name": category.name,
                    "is_check_box": false,
                    "item_lower_limit": Number(category.min || 0),
                    "item_limit": Number(category.max || 1),
                    "position": null,
                    "customize_options": category.items.map(v => ({
                        "option_id": null,
                        "customize_id": null,
                        "name": v.name,
                        "price": Number(v.price || 0),
                        "is_active": 1,
                        "display_order": null
                    })),
                    "is_active": 1
                },
            }, { headers: base_headers() });
            console.log(group_menu_resp.data)
        }

        // for (const category of categoriesToUpdate) {
        //     const grabCategory = grabCategories.find(c => text_compare(c.name, category.name));
        //     const group_menu_update_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/update_merchant_item_customize`, {
        //         ...base_body({ site_id, access_token }),
        //         "item_customize": {
        //             "customize_id": grabCategory.id,
        //             "restaurant_item_id": 0,
        //             "restaurant_id": null,
        //             "name": category.name,
        //             "is_check_box": true,
        //             "item_lower_limit": Number(category.min || 0),
        //             "item_limit": Number(category.max || 1),
        //             // "position": null,
        //             "customize_options": category.items.map(v => ({
        //                 "option_id": null,
        //                 "customize_id": grabCategory.id,
        //                 "name": v.name,
        //                 "price": v.price,
        //                 "is_active": 1,
        //                 "display_order": null
        //             })),
        //             "is_active": 1
        //         },
        //     }, { headers: base_headers() });
        //     console.log(group_menu_update_resp.data)
        // }
    } catch (err) {
        console.log(err);
        return null;
    }
};


module.exports = {
    sync_brand_menu,
    sync_brand_menu_option_items,
    sync_site_menu,
    sync_brand_promotion,
}