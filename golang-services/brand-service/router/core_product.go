package router

import (
	"bytes"
	"fmt"
	"math"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/xuri/excelize/v2"
)

// Generate a random string of specified length from the given charset
func generateRandomString(length int, charset string) string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[r.Intn(len(charset))]
	}
	return string(b)
}

// GetCoreProducts godoc
// @Summary Get core products
// @Description Retrieves a list of core products for a brand
// @Tags core-products
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Param source query string false "Filter by source"
// @Param category query string false "Filter by category"
// @Param available_for_sale query bool false "Filter by availability for sale"
// @Param search query string false "Search by name, code, or barcode"
// @Param types query string false "Filter by types (comma-separated)"
// @Success 200 {object} map[string]any "Returns paginated list of core products"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-products [get]
func GetCoreProducts(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	if limit > 100 {
		limit = 100 // Max 100 items per page
	}
	offset := (page - 1) * limit

	// Build query
	query := db.Model(&models.CoreProduct{}).Where("brand_id = ?", brandID)

	// Apply filters
	if source := c.Query("source"); source != "" {
		query = query.Where("source = ?", source)
	}
	if category := c.Query("category"); category != "" {
		query = query.Where("category = ?", category)
	}
	if availableForSale := c.Query("available_for_sale"); availableForSale != "" {
		query = query.Where("available_for_sale = ?", availableForSale == "true")
	}
	if types := c.QueryArray("types"); len(types) > 0 {
		query = query.Where("type IN ?", types)
	}
	if search := c.Query("search"); search != "" {
		query = query.Where(
			db.Where("name ILIKE ?", "%"+search+"%").
				Or("code ILIKE ?", "%"+search+"%").
				Or("bar_code ILIKE ?", "%"+search+"%"),
		)
	}

	// Count total items
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Get paginated results
	var coreProducts []models.CoreProduct
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&coreProducts).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	c.JSON(http.StatusOK, models.PaginationResponse{
		Success:    true,
		Data:       coreProducts,
		Total:      total,
		Page:       page,
		PageSize:   limit,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	})
}

// CreateCoreProduct godoc
// @Summary Create a core product
// @Description Creates a new core product for a brand
// @Tags core-products
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param core_product body models.CoreProduct true "Core product data"
// @Success 200 {object} map[string]any "Returns the created core product"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Brand not found"
// @Router /brands/{brand_id}/core-products [post]
func CreateCoreProduct(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Check if brand exists
	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Parse request body
	var coreProduct models.CoreProduct
	if err := c.ShouldBindJSON(&coreProduct); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Set brand ID
	coreProduct.BrandID = brandID

	// Generate code if not provided
	if coreProduct.Code == "" {
		coreProduct.Code = generateRandomString(8, "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
	}

	// Check if code already exists
	var existingProduct models.CoreProduct
	if err := db.Where("code = ? AND brand_id = ?", coreProduct.Code, brandID).First(&existingProduct).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "core_product_code_existed",
		})
		return
	}

	// Validate ingredients if provided
	if len(coreProduct.Ingredients) > 0 {
		var ingredientCodes []string
		for _, ingredient := range coreProduct.Ingredients {
			ingredientCodes = append(ingredientCodes, ingredient.Code)
		}

		var existingIngredients []models.CoreProduct
		if err := db.Where("code IN ? AND status = ? AND brand_id = ?", ingredientCodes, "active", brandID).Find(&existingIngredients).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "database_error",
			})
			return
		}

		if len(existingIngredients) != len(coreProduct.Ingredients) {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "ingredient_not_found",
			})
			return
		}
	}

	// Create core product
	if err := db.Create(&coreProduct).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    coreProduct,
	})
}

// UpdateCoreProduct godoc
// @Summary Update a core product
// @Description Updates an existing core product
// @Tags core-products
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param code path string true "Core product code"
// @Param core_product body models.CoreProduct true "Core product data"
// @Success 200 {object} map[string]any "Returns the updated core product"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Core product not found"
// @Router /brands/{brand_id}/core-products/{code} [put]
func UpdateCoreProduct(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")
	code := c.Param("code")

	// Parse request body
	var updateData models.CoreProduct
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Validate ingredients if provided
	if len(updateData.Ingredients) > 0 {
		var ingredientCodes []string
		for _, ingredient := range updateData.Ingredients {
			ingredientCodes = append(ingredientCodes, ingredient.Code)
		}

		var existingIngredients []models.CoreProduct
		if err := db.Where("code IN ? AND status = ? AND brand_id = ?", ingredientCodes, "active", brandID).Find(&existingIngredients).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "database_error",
			})
			return
		}

		if len(existingIngredients) != len(updateData.Ingredients) {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "ingredient_not_found",
			})
			return
		}
	}

	// Find and update the core product
	var coreProduct models.CoreProduct
	if err := db.Where("code = ? AND brand_id = ?", code, brandID).First(&coreProduct).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "core_product_not_found",
			})
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "database_error",
			})
		}
		return
	}

	// Update fields
	coreProduct.Name = updateData.Name
	coreProduct.Category = updateData.Category
	coreProduct.Unit = updateData.Unit
	coreProduct.Price = updateData.Price
	coreProduct.Images = updateData.Images
	coreProduct.SalePrice = updateData.SalePrice
	coreProduct.Weight = updateData.Weight
	coreProduct.Description = updateData.Description
	coreProduct.Height = updateData.Height
	coreProduct.AvailableForSale = updateData.AvailableForSale
	coreProduct.Type = updateData.Type
	coreProduct.Status = updateData.Status
	coreProduct.BarCode = updateData.BarCode
	coreProduct.Length = updateData.Length
	coreProduct.Ingredients = updateData.Ingredients
	coreProduct.Source = updateData.Source
	coreProduct.QuantityUnlimited = updateData.QuantityUnlimited

	// Save the updated core product
	if err := db.Save(&coreProduct).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    coreProduct,
	})
}

// DeleteCoreProduct godoc
// @Summary Delete a core product
// @Description Deletes an existing core product
// @Tags core-products
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param code path string true "Core product code"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Core product not found"
// @Router /brands/{brand_id}/core-products/{code} [delete]
func DeleteCoreProduct(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")
	code := c.Param("code")

	// Delete the core product
	result := db.Where("code = ? AND brand_id = ?", code, brandID).Delete(&models.CoreProduct{})
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "core_product_not_found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// GetCoreProductDetails godoc
// @Summary Get core product details
// @Description Retrieves details of a specific core product
// @Tags core-products
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param code path string true "Core product code"
// @Success 200 {object} map[string]any "Returns the core product details"
// @Failure 404 {object} map[string]any "Core product not found"
// @Router /brands/{brand_id}/core-products/{code} [get]
func GetCoreProductDetails(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")
	code := c.Param("code")

	// Find the core product
	var coreProduct models.CoreProduct
	if err := db.Where("code = ? AND brand_id = ?", code, brandID).First(&coreProduct).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "core_product_not_found",
			})
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "database_error",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    coreProduct,
	})
}

// GetCoreProductCategories godoc
// @Summary Get core product categories
// @Description Retrieves a list of core product categories for a brand
// @Tags core-product-categories
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} map[string]any "Returns paginated list of core product categories"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-product-categories [get]
func GetCoreProductCategories(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	if limit > 100 {
		limit = 100 // Max 100 items per page
	}
	offset := (page - 1) * limit

	// Count total items
	var total int64
	if err := db.Model(&models.CoreProductCategory{}).Where("brand_id = ?", brandID).Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Get paginated results
	var categories []models.CoreProductCategory
	if err := db.Where("brand_id = ?", brandID).Order("name ASC").Offset(offset).Limit(limit).Find(&categories).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	c.JSON(http.StatusOK, models.PaginationResponse{
		Success:    true,
		Data:       categories,
		Total:      total,
		Page:       page,
		PageSize:   limit,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	})
}

// CreateCoreProductCategory godoc
// @Summary Create a core product category
// @Description Creates a new core product category for a brand
// @Tags core-product-categories
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param category body models.CoreProductCategory true "Core product category data"
// @Success 200 {object} map[string]any "Returns the created core product category"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-product-categories [post]
func CreateCoreProductCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse request body
	var category models.CoreProductCategory
	if err := c.ShouldBindJSON(&category); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Set brand ID
	category.BrandID = brandID

	// Check if category already exists
	var existingCategory models.CoreProductCategory
	if err := db.Where("name = ? AND brand_id = ?", category.Name, brandID).First(&existingCategory).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "core_product_category_existed",
		})
		return
	}

	// Create category
	if err := db.Create(&category).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    category,
	})
}

// DeleteCoreProductCategory godoc
// @Summary Delete a core product category
// @Description Deletes an existing core product category
// @Tags core-product-categories
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param id path string true "Category ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Category not found"
// @Router /brands/{brand_id}/core-product-categories/{id} [delete]
func DeleteCoreProductCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")
	id := c.Param("id")

	// Delete the category
	result := db.Where("id = ? AND brand_id = ?", id, brandID).Delete(&models.CoreProductCategory{})
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "core_product_category_not_found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// GetCoreProductSources godoc
// @Summary Get core product sources
// @Description Retrieves a list of core product sources for a brand
// @Tags core-product-sources
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} map[string]any "Returns paginated list of core product sources"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-product-sources [get]
func GetCoreProductSources(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	if limit > 100 {
		limit = 100 // Max 100 items per page
	}
	offset := (page - 1) * limit

	// Count total items
	var total int64
	if err := db.Model(&models.CoreProductSource{}).Where("brand_id = ?", brandID).Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Get paginated results
	var sources []models.CoreProductSource
	if err := db.Where("brand_id = ?", brandID).Order("name ASC").Offset(offset).Limit(limit).Find(&sources).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	hasNextPage := page < totalPages
	hasPrevPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"success":       true,
		"data":          sources,
		"total":         total,
		"limit":         limit,
		"page":          page,
		"pages":         totalPages,
		"has_next_page": hasNextPage,
		"has_prev_page": hasPrevPage,
	})
}

// CreateCoreProductSource godoc
// @Summary Create a core product source
// @Description Creates a new core product source for a brand
// @Tags core-product-sources
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param source body models.CoreProductSource true "Core product source data"
// @Success 200 {object} map[string]any "Returns the created core product source"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-product-sources [post]
func CreateCoreProductSource(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse request body
	var source models.CoreProductSource
	if err := c.ShouldBindJSON(&source); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Set brand ID
	source.BrandID = brandID

	// Check if source already exists
	var existingSource models.CoreProductSource
	if err := db.Where("name = ? AND brand_id = ?", source.Name, brandID).First(&existingSource).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "core_product_source_existed",
		})
		return
	}

	// Create source
	if err := db.Create(&source).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    source,
	})
}

// DeleteCoreProductSource godoc
// @Summary Delete a core product source
// @Description Deletes an existing core product source
// @Tags core-product-sources
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param id path string true "Source ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Source not found"
// @Router /brands/{brand_id}/core-product-sources/{id} [delete]
func DeleteCoreProductSource(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")
	id := c.Param("id")

	// Delete the source
	result := db.Where("id = ? AND brand_id = ?", id, brandID).Delete(&models.CoreProductSource{})
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "core_product_source_not_found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// GetCoreProductUnits godoc
// @Summary Get core product units
// @Description Retrieves a list of core product units for a brand
// @Tags core-product-units
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} map[string]any "Returns paginated list of core product units"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-product-units [get]
func GetCoreProductUnits(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	if limit > 100 {
		limit = 100 // Max 100 items per page
	}
	offset := (page - 1) * limit

	// Count total items
	var total int64
	if err := db.Model(&models.CoreProductUnit{}).Where("brand_id = ?", brandID).Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Get paginated results
	var units []models.CoreProductUnit
	if err := db.Where("brand_id = ?", brandID).Order("name ASC").Offset(offset).Limit(limit).Find(&units).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	hasNextPage := page < totalPages
	hasPrevPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"success":       true,
		"data":          units,
		"total":         total,
		"limit":         limit,
		"page":          page,
		"pages":         totalPages,
		"has_next_page": hasNextPage,
		"has_prev_page": hasPrevPage,
	})
}

// CreateCoreProductUnit godoc
// @Summary Create a core product unit
// @Description Creates a new core product unit for a brand
// @Tags core-product-units
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param unit body models.CoreProductUnit true "Core product unit data"
// @Success 200 {object} map[string]any "Returns the created core product unit"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-product-units [post]
func CreateCoreProductUnit(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse request body
	var unit models.CoreProductUnit
	if err := c.ShouldBindJSON(&unit); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Set brand ID
	unit.BrandID = brandID

	// Check if unit already exists
	var existingUnit models.CoreProductUnit
	if err := db.Where("name = ? AND brand_id = ?", unit.Name, brandID).First(&existingUnit).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "core_product_unit_existed",
		})
		return
	}

	// Create unit
	if err := db.Create(&unit).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    unit,
	})
}

// DeleteCoreProductUnit godoc
// @Summary Delete a core product unit
// @Description Deletes an existing core product unit
// @Tags core-product-units
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param id path string true "Unit ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Unit not found"
// @Router /brands/{brand_id}/core-product-units/{id} [delete]
func DeleteCoreProductUnit(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")
	id := c.Param("id")

	// Delete the unit
	result := db.Where("id = ? AND brand_id = ?", id, brandID).Delete(&models.CoreProductUnit{})
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "core_product_unit_not_found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// GetImportTemplate godoc
// @Summary Get import template for core products
// @Description Returns a template structure for importing core products
// @Tags core-products
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Success 200 {object} map[string]any "Returns import template structure"
// @Router /brands/{brand_id}/core-products/import-template [get]
func GetImportTemplate(c *gin.Context) {
	// Get brand ID from URL parameter
	brandID := c.Param("brand_id")

	// Get brand information for the filename
	db := middlewares.GetDB(c)
	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Create a new Excel file
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Printf("Error closing Excel file: %v\n", err)
		}
	}()

	// Get the default sheet name (Sheet1) for core products
	productsSheetName := f.GetSheetName(0)

	// Rename the first sheet to make it more descriptive
	if err := f.SetSheetName(productsSheetName, "Core Products"); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "excel_sheet_rename_error",
			"details": err.Error(),
		})
		return
	}
	productsSheetName = "Core Products"

	// Create a second sheet for ingredients
	ingredientsSheetName := "Ingredients"
	if _, err := f.NewSheet(ingredientsSheetName); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "excel_sheet_creation_error",
			"details": err.Error(),
		})
		return
	}

	// Define headers and example data for Core Products sheet
	productHeaders := map[string]string{
		"A1": "Mã sản phẩm",             // Product Code
		"B1": "Tên sản phẩm(*)",         // Product Name (required)
		"C1": "Danh mục",                // Category
		"D1": "Đơn vị tính",             // Unit
		"E1": "Mã vạch",                 // Barcode
		"F1": "Giá vốn",                 // Price
		"G1": "Giá bán",                 // Sale Price
		"H1": "Cân nặng (g)",            // Weight
		"I1": "Chiều cao (cm)",          // Height
		"J1": "Chiều dài (cm)",          // Length
		"K1": "Mô tả",                   // Description
		"L1": "Trạng thái(*)",           // Status (required)
		"M1": "Loại",                    // Type
		"N1": "Cho phép bán(*)",         // Available For Sale (required)
		"O1": "Nguồn",                   // Source
		"P1": "Không giới hạn số lượng", // Quantity Unlimited
	}

	productExample := map[string]string{
		"A2": "SP001",
		"B2": "Cà phê sữa đá",
		"C2": "Đồ uống",
		"D2": "Ly",
		"E2": "8936008590199",
		"F2": "15000",
		"G2": "29000",
		"H2": "350",
		"I2": "12",
		"J2": "8",
		"K2": "Cà phê sữa đá nguyên chất",
		"L2": "active",
		"M2": "thanh_pham",
		"N2": "true",
		"O2": "inhouse",
		"P2": "true",
	}

	// Write headers and example data to Core Products sheet
	for cell, value := range productHeaders {
		if err := f.SetCellValue(productsSheetName, cell, value); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "excel_write_error",
				"details": err.Error(),
			})
			return
		}
	}

	for cell, value := range productExample {
		if err := f.SetCellValue(productsSheetName, cell, value); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "excel_write_error",
				"details": err.Error(),
			})
			return
		}
	}

	// Define headers and example data for Ingredients sheet
	ingredientHeaders := map[string]string{
		"A1": "Mã sản phẩm(*)",    // Product Code (required)
		"B1": "Mã nguyên liệu(*)", // Ingredient Code (required)
		"C1": "Số lượng(*)",       // Amount (required)
		"D1": "Đơn vị tính",       // Unit
	}

	ingredientExample := map[string]string{
		"A2": "SP001",
		"B2": "NL001",
		"C2": "30",
		"D2": "ml",
	}

	// Write headers and example data to Ingredients sheet
	for cell, value := range ingredientHeaders {
		if err := f.SetCellValue(ingredientsSheetName, cell, value); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "excel_write_error",
				"details": err.Error(),
			})
			return
		}
	}

	for cell, value := range ingredientExample {
		if err := f.SetCellValue(ingredientsSheetName, cell, value); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "excel_write_error",
				"details": err.Error(),
			})
			return
		}
	}

	// Add validation information to a new sheet
	validationSheetName := "Validations"
	if _, err := f.NewSheet(validationSheetName); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "excel_sheet_creation_error",
			"details": err.Error(),
		})
		return
	}

	// Write validation rules
	validationRules := [][]string{
		{"Field", "Valid Values"},
		{"Status", "active, inactive"},
		{"Type", "nguyen_lieu, ban_thanh_pham, thanh_pham"},
		{"Source", "inhouse, supplier"},
		{"Required Fields", "Tên sản phẩm, Trạng thái, Cho phép bán"},
	}

	for i, rule := range validationRules {
		cell1 := fmt.Sprintf("A%d", i+1)
		cell2 := fmt.Sprintf("B%d", i+1)
		if err := f.SetCellValue(validationSheetName, cell1, rule[0]); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "excel_write_error",
				"details": err.Error(),
			})
			return
		}
		if err := f.SetCellValue(validationSheetName, cell2, rule[1]); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "excel_write_error",
				"details": err.Error(),
			})
			return
		}
	}

	// Set column widths for better readability
	for sheet, cols := range map[string]int{
		productsSheetName:    16, // A-P
		ingredientsSheetName: 4,  // A-D
		validationSheetName:  2,  // A-B
	} {
		for i := 1; i <= cols; i++ {
			colName := toColumnName(i)
			if err := f.SetColWidth(sheet, colName, colName, 15); err != nil {
				fmt.Printf("Warning: Failed to set column width for %s sheet: %v\n", sheet, err)
			}
		}
	}

	// Generate a unique filename
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("core_products_template_%s_%s.xlsx", sanitizeFilename(brand.Name), timestamp)

	// Create a buffer to store the Excel file
	var buffer bytes.Buffer
	if err := f.Write(&buffer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "excel_write_error",
			"details": err.Error(),
		})
		return
	}

	// Upload the Excel file to storage
	storagePath := fmt.Sprintf("core-products/templates/%s", filename)
	fileURL, err := utils.UploadFile("nexpos-files", storagePath, buffer.Bytes())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "file_upload_error",
			"details": err.Error(),
		})
		return
	}

	// Return success response with file URL
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"file_url": fileURL,
			"filename": filename,
		},
	})
}

// ImportCoreProducts godoc
// @Summary Import core products
// @Description Imports core products from a file
// @Tags core-products
// @Accept multipart/form-data
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param file formData file true "Excel file with core products data"
// @Success 200 {object} map[string]any "Returns import results"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /brands/{brand_id}/core-products/import [post]
func ImportCoreProducts(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Initialize import result
	result := ImportResult{
		Success: true,
	}

	// Check if brand exists
	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Get file from form
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "file_required",
		})
		return
	}

	// Open the file
	src, err := file.Open()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "file_open_error",
		})
		return
	}
	defer src.Close()

	// Read the Excel file
	f, err := excelize.OpenReader(src)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_excel_file",
		})
		return
	}
	defer f.Close()

	// Begin transaction
	tx := db.Begin()
	if tx.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "transaction_start_error",
		})
		return
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Read products sheet
	products := make(map[string]*models.CoreProduct)
	rows, err := f.GetRows("Core Products")
	println("Rows:", rows)
	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "sheet_not_found",
			"message": "Core Products sheet not found",
		})
		return
	}

	result.TotalRows = len(rows) - 1 // Subtract header row

	// Process each row starting from index 1 (skip header)
	for i := 1; i < len(rows); i++ {
		result.ProcessedRows++
		row := rows[i]

		// Skip empty rows
		if len(row) == 0 || (len(row) > 0 && row[0] == "") {
			continue
		}

		// Parse row data
		product := &models.CoreProduct{
			BrandID: brandID,
		}

		// Map Excel columns to struct fields
		if len(row) > 0 {
			product.Code = strings.TrimSpace(row[0])
			if product.Code == "" {
				product.Code = generateRandomString(8, "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
			}
		}
		if len(row) > 1 {
			product.Name = strings.TrimSpace(row[1])
			if product.Name == "" {
				result.Errors = append(result.Errors, ImportError{
					Row:     i + 1,
					Column:  "B",
					Message: "Name is required",
				})
				continue
			}
		}
		if len(row) > 2 {
			product.Category = strings.TrimSpace(row[2])
		}
		if len(row) > 3 {
			product.Unit = strings.TrimSpace(row[3])
		}
		if len(row) > 4 {
			product.BarCode = strings.TrimSpace(row[4])
		}
		if len(row) > 5 {
			price, err := strconv.ParseFloat(strings.TrimSpace(row[5]), 64)
			if err == nil {
				product.Price = price
			}
		}
		if len(row) > 6 {
			salePrice, err := strconv.ParseFloat(strings.TrimSpace(row[6]), 64)
			if err == nil {
				product.SalePrice = salePrice
			}
		}
		if len(row) > 7 {
			weight, err := strconv.ParseFloat(strings.TrimSpace(row[7]), 64)
			if err == nil {
				product.Weight = weight
			}
		}
		if len(row) > 8 {
			height, err := strconv.ParseFloat(strings.TrimSpace(row[8]), 64)
			if err == nil {
				product.Height = height
			}
		}
		if len(row) > 9 {
			length, err := strconv.ParseFloat(strings.TrimSpace(row[9]), 64)
			if err == nil {
				product.Length = length
			}
		}
		if len(row) > 10 {
			product.Description = strings.TrimSpace(row[10])
		}
		if len(row) > 11 {
			status := strings.ToLower(strings.TrimSpace(row[11]))
			if status != "active" && status != "inactive" {
				result.Errors = append(result.Errors, ImportError{
					Row:     i + 1,
					Column:  "L",
					Message: "Invalid status. Must be 'active' or 'inactive'",
				})
				continue
			}
			product.Status = status
		}
		if len(row) > 12 {
			productType := strings.ToLower(strings.TrimSpace(row[12]))
			if productType != "" && productType != "nguyen_lieu" && productType != "ban_thanh_pham" && productType != "thanh_pham" {
				result.Errors = append(result.Errors, ImportError{
					Row:     i + 1,
					Column:  "M",
					Message: "Invalid type. Must be 'nguyen_lieu', 'ban_thanh_pham', or 'thanh_pham'",
				})
				continue
			}
			product.Type = productType
		}
		if len(row) > 13 {
			availableForSale, err := strconv.ParseBool(strings.TrimSpace(row[13]))
			if err != nil {
				result.Errors = append(result.Errors, ImportError{
					Row:     i + 1,
					Column:  "N",
					Message: "Invalid available for sale value. Must be true or false",
				})
				continue
			}
			product.AvailableForSale = availableForSale
		}
		if len(row) > 14 {
			source := strings.ToLower(strings.TrimSpace(row[14]))
			if source != "" && source != "inhouse" && source != "supplier" {
				result.Errors = append(result.Errors, ImportError{
					Row:     i + 1,
					Column:  "O",
					Message: "Invalid source. Must be 'inhouse' or 'supplier'",
				})
				continue
			}
			product.Source = source
		}
		if len(row) > 15 {
			quantityUnlimited, err := strconv.ParseBool(strings.TrimSpace(row[15]))
			if err == nil {
				product.QuantityUnlimited = quantityUnlimited
			}
		}

		// Store product for later ingredient processing
		products[product.Code] = product

		// Create or update the product
		if err := tx.Where("code = ? AND brand_id = ?", product.Code, brandID).
			Assign(product).
			FirstOrCreate(product).Error; err != nil {
			result.Errors = append(result.Errors, ImportError{
				Row:     i + 1,
				Column:  "A",
				Message: fmt.Sprintf("Database error: %v", err),
			})
			continue
		}

		result.SuccessfulRows++
	}

	// Process ingredients if they exist
	if len(result.Errors) == 0 { // Only process ingredients if no errors occurred during product import
		ingredientRows, err := f.GetRows("Ingredients")
		if err == nil && len(ingredientRows) > 1 { // Sheet exists and has data
			for i := 1; i < len(ingredientRows); i++ { // Skip header row
				row := ingredientRows[i]
				if len(row) < 3 { // Need at least product code, ingredient code, and amount
					continue
				}

				productCode := strings.TrimSpace(row[0])
				ingredientCode := strings.TrimSpace(row[1])
				amount, err := strconv.ParseFloat(strings.TrimSpace(row[2]), 64)
				if err != nil {
					result.Errors = append(result.Errors, ImportError{
						Row:     i + 1,
						Column:  "C",
						Message: "Invalid amount value",
					})
					continue
				}

				// Get the product
				product, exists := products[productCode]
				if !exists {
					result.Errors = append(result.Errors, ImportError{
						Row:     i + 1,
						Column:  "A",
						Message: "Product not found",
					})
					continue
				}

				// Add ingredient
				product.Ingredients = append(product.Ingredients, models.CoreProductIngredient{
					Code:   ingredientCode,
					Amount: amount,
					Unit:   strings.TrimSpace(row[3]), // Optional unit
				})

				// Update product with ingredients
				if err := tx.Save(product).Error; err != nil {
					result.Errors = append(result.Errors, ImportError{
						Row:     i + 1,
						Column:  "A",
						Message: fmt.Sprintf("Failed to save ingredients: %v", err),
					})
					break // Break on first error to avoid partial updates
				}
			}
		}
	}

	result.FailedRows = result.ProcessedRows - result.SuccessfulRows
	result.Success = len(result.Errors) == 0

	if !result.Success {
		tx.Rollback()

		// Create a more descriptive error message summarizing the issues
		errorMessage := fmt.Sprintf("Import failed: %d of %d rows had errors",
			result.FailedRows, result.ProcessedRows)

		// If there are specific errors, include the first few in the message
		if len(result.Errors) > 0 {
			errorMessage += ". Issues include: "
			// Add up to 3 error details to avoid overly long messages
			maxErrors := int(math.Min(3, float64(len(result.Errors))))
			for i := 0; i < maxErrors; i++ {
				err := result.Errors[i]
				errorMessage += fmt.Sprintf("Row %d: %s; ", err.Row, err.Message)
			}

			// Indicate if there are more errors not shown
			if len(result.Errors) > 3 {
				errorMessage += fmt.Sprintf("and %d more issues", len(result.Errors)-3)
			}
		}

		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   errorMessage,
			"data":    result,
		})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "transaction_commit_error",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// GetExportTemplate godoc
// @Summary Get export template for core products
// @Description Returns a template structure for exporting core products
// @Tags core-products
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Success 200 {object} map[string]any "Returns export template structure"
// @Router /brands/{brand_id}/core-products/export-template [get]
func ExportCoreProducts(c *gin.Context) {
	// Get brand ID from URL parameter
	brandID := c.Param("brand_id")

	// Get brand information for the filename
	db := middlewares.GetDB(c)
	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Create a new Excel file
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Printf("Error closing Excel file: %v\n", err)
		}
	}()

	// Get the default sheet name (Sheet1) for core products
	productsSheetName := f.GetSheetName(0)

	// Rename the first sheet to make it more descriptive
	if err := f.SetSheetName(productsSheetName, "Core Products"); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "excel_sheet_rename_error",
			"details": err.Error(),
		})
		return
	}
	productsSheetName = "Core Products"

	// Create a second sheet for ingredients
	ingredientsSheetName := "Ingredients"
	if _, err := f.NewSheet(ingredientsSheetName); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "excel_sheet_creation_error",
			"details": err.Error(),
		})
		return
	}

	// Define column headers for the Core Products sheet
	productHeaders := []string{
		"Code", "Name", "Category", "Unit", "Bar Code",
		"Price", "Sale Price", "Weight", "Height", "Length",
		"Description", "Status", "Type", "Available For Sale",
		"Source", "Quantity Unlimited", "Created At", "Updated At",
	}

	// Write headers to the first row of the core products sheet
	for i, header := range productHeaders {
		colName := toColumnName(i + 1)
		cell := colName + "1"
		if err := f.SetCellValue(productsSheetName, cell, header); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "excel_write_error",
				"details": err.Error(),
			})
			return
		}
	}

	// Define column headers for the Ingredients sheet
	ingredientHeaders := []string{
		"Product Code", "Product Name", "Ingredient Code", "Ingredient Name", "Amount", "Unit",
	}

	// Write headers to the first row of the ingredients sheet
	for i, header := range ingredientHeaders {
		colName := toColumnName(i + 1)
		cell := colName + "1"
		if err := f.SetCellValue(ingredientsSheetName, cell, header); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "excel_write_error",
				"details": err.Error(),
			})
			return
		}
	}

	// Query products using a cursor to stream results efficiently
	rows, err := db.Model(&models.CoreProduct{}).Where("brand_id = ?", brandID).Rows()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_query_error",
			"details": err.Error(),
		})
		return
	}
	defer rows.Close()

	// Process rows one by one to minimize memory usage
	productRowNum := 2    // Start from row 2 (row 1 has headers)
	ingredientRowNum := 2 // Start from row 2 (row 1 has headers)

	// Store product codes and names to reference when writing ingredients
	productCodeToName := make(map[string]string)

	for rows.Next() {
		// Scan the current row into a new product struct
		var product models.CoreProduct
		if err := db.ScanRows(rows, &product); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "database_scan_error",
				"details": err.Error(),
			})
			return
		}

		// Store product code and name for ingredient sheet
		productCodeToName[product.Code] = product.Name

		// Format time values for better readability
		createdAt := formatTime(product.CreatedAt)
		updatedAt := formatTime(product.UpdatedAt)

		// Prepare row data in the same order as headers
		rowData := []interface{}{
			product.Code,
			product.Name,
			product.Category,
			product.Unit,
			product.BarCode,
			product.Price,
			product.SalePrice,
			product.Weight,
			product.Height,
			product.Length,
			product.Description,
			product.Status,
			product.Type,
			product.AvailableForSale,
			product.Source,
			product.QuantityUnlimited,
			createdAt,
			updatedAt,
		}

		// Write data to the current row in products sheet
		for j, value := range rowData {
			colName := toColumnName(j + 1)
			cell := colName + strconv.Itoa(productRowNum)
			if err := f.SetCellValue(productsSheetName, cell, value); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"error":   "excel_data_write_error",
					"details": err.Error(),
				})
				return
			}
		}

		productRowNum++

		// Process ingredients for this product if there are any
		if len(product.Ingredients) > 0 {
			for _, ingredient := range product.Ingredients {
				// Get ingredient details if needed
				var ingredientProduct models.CoreProduct
				if err := db.Where("code = ? AND brand_id = ?", ingredient.Code, brandID).First(&ingredientProduct).Error; err != nil {
					// Just log the error and continue - don't stop the export if one ingredient can't be found
					fmt.Printf("Warning: Could not find ingredient with code %s: %v\n", ingredient.Code, err)
					continue
				}

				// Prepare ingredient row data
				ingredientData := []interface{}{
					product.Code,           // Product Code
					product.Name,           // Product Name
					ingredient.Code,        // Ingredient Code
					ingredientProduct.Name, // Ingredient Name
					ingredient.Amount,      // Quantity
					ingredientProduct.Unit, // Unit
				}

				// Write ingredient data to the ingredients sheet
				for j, value := range ingredientData {
					colName := toColumnName(j + 1)
					cell := colName + strconv.Itoa(ingredientRowNum)
					if err := f.SetCellValue(ingredientsSheetName, cell, value); err != nil {
						c.JSON(http.StatusInternalServerError, gin.H{
							"success": false,
							"error":   "excel_ingredient_write_error",
							"details": err.Error(),
						})
						return
					}
				}

				ingredientRowNum++
			}
		}
	}

	// Check for any errors during row iteration
	if err := rows.Err(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "database_iteration_error",
			"details": err.Error(),
		})
		return
	}

	// Adjust column widths for better readability in products sheet
	for i := range productHeaders {
		colName := toColumnName(i + 1)
		if err := f.SetColWidth(productsSheetName, colName, colName, 15); err != nil {
			// Non-critical error, log and continue
			fmt.Printf("Warning: Failed to set column width for products sheet: %v\n", err)
		}
	}

	// Adjust column widths for better readability in ingredients sheet
	for i := range ingredientHeaders {
		colName := toColumnName(i + 1)
		if err := f.SetColWidth(ingredientsSheetName, colName, colName, 15); err != nil {
			// Non-critical error, log and continue
			fmt.Printf("Warning: Failed to set column width for ingredients sheet: %v\n", err)
		}
	}

	// Generate a unique, sanitized filename with brand name and timestamp
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("core_products_%s_%s.xlsx", sanitizeFilename(brand.Name), timestamp)

	// Create a buffer to store the Excel file
	var buffer bytes.Buffer
	if err := f.Write(&buffer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "excel_write_error",
			"details": err.Error(),
		})
		return
	}

	// Upload the Excel file to storage using the existing UploadFile utility
	storagePath := fmt.Sprintf("core-products/%s", filename)
	fileURL, err := utils.UploadFile("nexpos-files", storagePath, buffer.Bytes())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "file_upload_error",
			"details": err.Error(),
		})
		return
	}

	// Return a JSON response with the download URL
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"file_url": fileURL,
			"filename": filename,
		},
	})
}

// toColumnName converts a 1-based column number to an Excel column name (A, B, C, ... Z, AA, AB, etc.)
func toColumnName(colNum int) string {
	var result string
	for colNum > 0 {
		remainder := (colNum - 1) % 26
		result = string(rune('A'+remainder)) + result
		colNum = (colNum - 1) / 26
	}
	return result
}

// sanitizeFilename removes invalid characters from a filename
// to ensure it's compatible with all file systems
func sanitizeFilename(name string) string {
	return strings.Map(func(r rune) rune {
		if r >= 'a' && r <= 'z' || r >= 'A' && r <= 'Z' || r >= '0' && r <= '9' || r == '_' || r == '-' || r == '.' {
			return r
		}
		return '_'
	}, name)
}

// formatTime converts a time.Time value to a formatted string
// Returns empty string for zero time values
func formatTime(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// ImportError represents an error during import
type ImportError struct {
	Row     int    `json:"row"`
	Column  string `json:"column"`
	Message string `json:"message"`
}

// ImportResult represents the result of an import operation
type ImportResult struct {
	Success        bool          `json:"success"`
	TotalRows      int           `json:"total_rows"`
	ProcessedRows  int           `json:"processed_rows"`
	SuccessfulRows int           `json:"successful_rows"`
	FailedRows     int           `json:"failed_rows"`
	Errors         []ImportError `json:"errors"`
}

// ExcelRowProduct represents a row in the Excel import file
type ExcelRowProduct struct {
	Code              string  `json:"code"`
	Name              string  `json:"name"`
	Category          string  `json:"category"`
	Unit              string  `json:"unit"`
	BarCode           string  `json:"bar_code"`
	Price             float64 `json:"price"`
	SalePrice         float64 `json:"sale_price"`
	Weight            float64 `json:"weight"`
	Height            float64 `json:"height"`
	Length            float64 `json:"length"`
	Description       string  `json:"description"`
	Status            string  `json:"status"`
	Type              string  `json:"type"`
	AvailableForSale  bool    `json:"available_for_sale"`
	Source            string  `json:"source"`
	QuantityUnlimited bool    `json:"quantity_unlimited"`
}

// ExcelRowIngredient represents an ingredient row in the Excel import file
type ExcelRowIngredient struct {
	ProductCode    string  `json:"product_code"`
	IngredientCode string  `json:"ingredient_code"`
	Amount         float64 `json:"amount"`
	Unit           string  `json:"unit"`
}
