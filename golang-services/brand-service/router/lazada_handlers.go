package router

import (
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/lazada"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/lazada/iop"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// GetLazadaAuthURL generates and returns an authorization URL for Lazada integration
func GetLazadaAuthURL(c *gin.Context) {
	// Define request structure with appropriate JSON tags
	type AuthURLRequest struct {
		BrandID     string `json:"brand_id" binding:"required"`
		CallbackURL string `json:"callback_url" binding:"required"`
	}

	// Parse request body
	var req AuthURLRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if req.BrandID == "" || req.CallbackURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Missing required fields",
			"details": "brand_id and callback_url are required",
		})
		return
	}

	// Validate callback URL format
	_, err := url.Parse(req.CallbackURL)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid callback URL format",
		})
		return
	}

	// Create an IOP client directly to generate the auth URL
	iopClient := iop.NewClient(&iop.ClientOptions{
		APIKey:    iop.AppKey,
		APISecret: iop.AppSecret,
		Region:    "VN",
	})

	// Set callback URL to be used when generating the auth URL
	iopClient.SetCallbackUrl(req.CallbackURL)

	// Generate a state token to prevent CSRF attacks
	stateToken := uuid.New().String()

	// Generate the authorization URL
	authURL := iopClient.MakeAuthURL()

	// Return success response with the generated auth URL
	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"auth_url": authURL,
		"state":    stateToken,
	})
}

// HandleLazadaCallback processes the callback from Lazada after user authorization
func HandleLazadaCallback(c *gin.Context) {
	// Define response structure for consistent API responses
	type CallbackResponse struct {
		Success   bool   `json:"success"`              // Whether the operation was successful
		Message   string `json:"message,omitempty"`    // Success or error message
		Error     string `json:"error,omitempty"`      // Error details (only set when success is false)
		SellerID  string `json:"seller_id,omitempty"`  // The Lazada seller ID
		StoreID   string `json:"store_id,omitempty"`   // The created/updated site ID in our system
		StoreName string `json:"store_name,omitempty"` // The Lazada seller name
	}

	// Handle POST request with JSON body
	var req struct {
		Code    string `json:"code" binding:"required"`     // Authorization code from Lazada
		BrandID string `json:"brand_id" binding:"required"` // Our internal brand ID
		HubID   string `json:"hub_id" binding:"required"`   // Hub ID to assign the site to
	}

	// Parse the request body into the request struct
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, CallbackResponse{
			Success: false,
			Error:   "Invalid request format",
			Message: "Required parameters missing or invalid. Ensure code, brand_id, and hub_id are provided.",
		})
		return
	}

	// Create a Lazada client
	client := lazada.NewLazadaClient()

	// Create a token object with the authorization code
	token := &models.Token{
		Params: map[string]string{
			"code": req.Code,
		},
	}

	// Exchange the authorization code for access and refresh tokens
	err := client.GetToken(token)
	if err != nil {
		c.JSON(http.StatusInternalServerError, CallbackResponse{
			Success: false,
			Error:   "Authorization failed",
			Message: fmt.Sprintf("Failed to authorize with Lazada: %v", err),
		})
		return
	}

	// Get store details using the access token
	storeDetail, err := client.GetStore(token)
	if err != nil {
		c.JSON(http.StatusInternalServerError, CallbackResponse{
			Success: false,
			Error:   "Failed to get store details",
			Message: fmt.Sprintf("Error retrieving Lazada store information: %v", err),
		})
		return
	}

	// Get database connection
	db := middlewares.GetDB(c)

	// Check if brand exists
	var brand models.Brand
	if err := db.Where("id = ?", req.BrandID).First(&brand).Error; err != nil {
		c.JSON(http.StatusBadRequest, CallbackResponse{
			Success: false,
			Error:   "Brand not found",
			Message: fmt.Sprintf("Could not find brand with ID: %s", req.BrandID),
		})
		return
	}

	// Generate unique code and name for the site
	storeName := storeDetail.Name
	if storeName == "" {
		storeName = fmt.Sprintf("Lazada Store %s", storeDetail.ID)
	}
	siteCode := fmt.Sprintf("LAZADA_%s", storeDetail.ID)

	// Check if site already exists
	site := models.Site{}
	if err := db.Where("code = ?", siteCode).FirstOrInit(&site).Error; err != nil {
		c.JSON(http.StatusInternalServerError, CallbackResponse{
			Success: false,
			Error:   "Database error",
			Message: fmt.Sprintf("Failed to initialize site record: %v", err),
		})
		return
	}

	// Calculate token expiration time based on expires_in value
	var expiredAt time.Time
	if expiresInStr, ok := token.Params["expires_in"]; ok {
		if expiresIn, err := strconv.Atoi(expiresInStr); err == nil {
			expiredAt = time.Now().Add(time.Duration(expiresIn) * time.Second)
		} else {
			// Default to 30 days if parsing fails
			expiredAt = time.Now().AddDate(0, 0, 30)
		}
	} else {
		// Default expiration time (30 days) if not provided
		expiredAt = time.Now().AddDate(0, 0, 30)
	}

	// Create token code
	tokenCode := fmt.Sprintf("%s_lazada", storeDetail.ID)

	// Set site properties
	site = models.Site{
		// Preserve existing ID if site already exists
		ID: site.ID,

		// Core identification
		BrandID:    req.BrandID,
		HubID:      req.HubID,
		Name:       storeName,
		Code:       siteCode,
		Type:       "mart",
		MainSource: "lazada",

		// Basic information
		Address:     storeDetail.Address,
		Description: "",

		// Required fields with default values
		Active:      true,
		AutoConfirm: false,
		AutoPrint:   false,

		// Empty JSON fields
		AddressObj: models.JSONField[models.AddressObj]{},
		Tokens:     models.JSONArray[models.SiteToken]{},
		HubIDs:     models.JSONArray[string]{},

		// Settings
		ApplyCommission:          false,
		ApplyGift:                false,
		UseCoreProduct:           false,
		EnableLocalOrderShipping: false,
		IsHeadSite:               false,
		IsHESite:                 false,
		PreparationTimePerOrder:  0,

		// Initialize PauseApps with empty map
		PauseApps: models.JSONField[map[string]bool]{
			Data: make(map[string]bool),
		},
	}

	// Create or update TokenAccount record
	tokenAccount := models.TokenAccount{
		TokenCode:    tokenCode,
		Source:       "lazada",
		SiteID:       storeDetail.ID,
		SiteName:     storeName,
		AccessToken:  token.AccessToken,
		RefreshToken: token.RefreshToken,
		ExpiredAt:    expiredAt,
	}

	// First try to update existing record, then create if it doesn't exist
	result := db.Model(&models.TokenAccount{}).
		Where("token_code = ?", tokenCode).
		Where("source = ?", "lazada").
		Updates(tokenAccount)

	if result.Error != nil {
		// Log error but continue with site creation
		fmt.Printf("Warning: Failed to update TokenAccount: %v\n", result.Error)
	} else if result.RowsAffected == 0 {
		// No existing record was updated, so create a new one
		if err := db.Create(&tokenAccount).Error; err != nil {
			// Log error but continue with site creation
			fmt.Printf("Warning: Failed to create TokenAccount: %v\n", err)
		}
	}

	// Save the site to the database
	var saveErr error
	if site.ID == "" {
		// This is a new site (Create)
		saveErr = db.Create(&site).Error
	} else {
		// This is an existing site (Update)
		saveErr = db.Save(&site).Error
	}

	if saveErr != nil {
		// Log the error details for debugging
		fmt.Printf("Database error saving site: %v\n", saveErr)

		// Return a structured error response to the client
		c.JSON(http.StatusInternalServerError, CallbackResponse{
			Success: false,
			Error:   "Failed to save site in database",
			Message: fmt.Sprintf("Database error: %v", saveErr),
		})
		return
	}

	// Return a success response with relevant store information
	c.JSON(http.StatusOK, CallbackResponse{
		Success:   true,
		Message:   "Successfully connected to Lazada",
		SellerID:  storeDetail.ID,
		StoreName: storeName,
		StoreID:   site.ID,
	})
}
