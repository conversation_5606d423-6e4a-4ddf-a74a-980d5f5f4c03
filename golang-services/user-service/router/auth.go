// Package router handles HTTP routing and request handling for the user service
package router

import (
	"context"
	"fmt"
	"net/http"

	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/email"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
	"github.com/google/uuid"
	"github.com/nyaruka/phonenumbers"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// Login authenticates a user with email/phone and password
func Login(c *gin.Context) {
	// Get database connection from middleware
	db := middlewares.GetDB(c)

	// Define request structure
	var request struct {
		Email    string `json:"email"`
		Phone    string `json:"phone"`
		Password string `json:"password"`
		FCMToken string `json:"fcm_token"`
	}

	// Parse request body
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Determine username from email or phone
	username := request.Email
	if username == "" {
		username = request.Phone
	}

	// Check if username is provided
	if username == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "username_required",
		})
		return
	}

	// Find user in database
	var user models.User
	if err := db.Where("username = ?", username).First(&user).Error; err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   "user_not_found",
		})
		return
	}

	// Check if password is set
	if user.Password == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "password_not_set",
		})
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(request.Password)); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   "wrong_password",
		})
		return
	}

	// Check if account is expired
	if user.ExpiredAt != nil && user.ExpiredAt.Before(time.Now()) {
		user.Status = models.UserStatusInactive
		db.Save(&user)
	}

	// Check if account is active
	if user.Status != models.UserStatusActive {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   "account_not_active",
		})
		return
	}

	// Track login device
	userAgent := c.Request.Header.Get("User-Agent")
	user.LastLoginDevice = userAgent

	var lastLoginDevices = user.LastLoginDevices

	if !utils.Contains(lastLoginDevices, userAgent) {
		lastLoginDevices = append(lastLoginDevices, userAgent)
		user.LastLoginDevices = lastLoginDevices
	}

	// Handle FCM token
	if request.FCMToken != "" {
		var fcmToken models.FCMToken
		result := db.Where("user_id = ? AND fcm_token = ?", user.ID, request.FCMToken).First(&fcmToken)

		if result.Error != nil {
			// Create new FCM token
			fcmToken = models.FCMToken{
				UserID:    string(user.ID),
				FCMToken:  request.FCMToken,
				UserAgent: userAgent,
			}
			db.Create(&fcmToken)

			// Subscribe to topic
			utils.SubscribeToTopic(request.FCMToken, "topic_new_order")
		}
	}

	// Generate authentication data
	authData, err := getUserAuth(db, &user)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Set authentication cookie
	c.SetCookie(utils.GetEnv("COOKIE_TOKEN_KEY", "auth_token"),
		authData.AccessToken,
		30*24*60*60, // 30 days
		"/",
		"",
		utils.GetEnv("ENV", "") == "prod",
		true)

	// Return successful response
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    authData,
	})
}

// Register creates a new user account
func Register(c *gin.Context) {
	// Get database connection from middleware
	db := middlewares.GetDB(c)

	// Define request structure
	var request struct {
		Name         string  `json:"name"`
		Email        string  `json:"email"`
		Phone        string  `json:"phone"`
		Address      string  `json:"address"`
		Password     string  `json:"password"`
		RedirectType *string `json:"redirect_type"`
	}

	// Parse request body
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Validate email or phone is provided
	if request.Email == "" && request.Phone == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "email_or_phone_is_required",
		})
		return
	}

	// Validate email format
	if request.Email != "" && !utils.IsValidEmail(request.Email) {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "email_invalid",
		})
		return
	}

	// Validate phone number
	if request.Phone != "" {
		num, err := phonenumbers.Parse(request.Phone, "VN")
		if err != nil || !phonenumbers.IsValidNumber(num) {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "phone_invalid",
			})
			return
		}
	}

	// Determine username from email or phone
	username := request.Email
	if username == "" {
		username = request.Phone
	}

	// Check if user already exists
	var existingUser models.User
	if db.Where("username = ?", username).First(&existingUser).Error == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "user_is_existed",
		})
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(request.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to hash password",
		})
		return
	}

	// Create new user
	user := models.User{
		// ID:               utils.GenObjectID(),
		Username: username,
		Name:     request.Name,
		Email:    request.Email,
		Phone:    request.Phone,
		Address:  request.Address,
		Password: string(hashedPassword),
		Role:     models.OwnerRoleDef.ID,
		Status:   models.UserStatusPending,
		// Hubs:             models.JSONArray[string]{}, // Initialize as empty slice
		// Brands:           models.JSONArray[string]{}, // Initialize as empty slice
		// Sites:            models.JSONArray[string]{}, // Initialize as empty slice
		// LastLoginDevices: models.JSONArray[string]{}, // Initialize as empty slice
	}

	// Start a transaction to ensure user and subscription are created together
	tx := db.Begin()

	// Create the user
	if err := tx.Create(&user).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Create a free subscription for the new user
	// Get the free plan details
	freePlan, exists := models.GetPlanByID(models.FreePlanID)
	if exists {
		// Create a perpetual free subscription without end date or period
		userSubscription := models.UserSubscription{
			ID:                 models.ObjectID(utils.GenObjectID()),
			UserID:             string(user.ID),
			SubscriptionPlanID: models.FreePlanID,
			Status:             models.Active,
			StartDate:          time.Now(),
			EndDate:            nil, // No end date for perpetual free subscriptions
			PaymentMethod:      "Free",
			Limits:             &models.JSONField[models.FeatureLimits]{Data: freePlan.Limits},
			// Period field is omitted for perpetual free subscriptions
		}

		if err := tx.Create(&userSubscription).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to create free subscription: " + err.Error(),
			})
			return
		}
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Transaction failed: " + err.Error(),
		})
		return
	}

	// Generate authentication data
	// authData, err := getUserAuth(db, &user)
	// if err != nil {
	// 	c.JSON(http.StatusBadRequest, gin.H{
	// 		"success": false,
	// 		"error":   err.Error(),
	// 	})
	// 	return
	// }

	// Generate OTP
	otp := "123456"
	if utils.GetEnv("USE_MERCHANT_APPS", "") == "true" {
		otp = utils.GenerateOTP(6)
	}

	// Set OTP expiry time
	otpExpiry := time.Now().Add(15 * time.Minute)
	userOTP := models.UserOTP{
		ID:         utils.GenObjectID(),
		VerifyType: "register",
		UserUID:    username,
		OTP:        otp,
		ExpiredAt:  otpExpiry,
	}

	// Send verification email
	email.SendEmail(user.Email, "Kích hoạt tài khoản", "register", map[string]any{
		"name": user.Name,
		"code": otp,
		"url": fmt.Sprintf("%s/verify-account?code=%s&id=%s&type=register",
			utils.GetWebURLToVerifyAccount(request.RedirectType), otp, user.ID),
	})

	// Save OTP in database
	if err := db.Create(&userOTP).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Return successful response
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    user,
	})
}

// VerifyToken validates a JWT token
func VerifyToken(c *gin.Context) {
	// Get token from header or cookie
	token := c.GetHeader("x-access-token")

	if token == "" {
		// Try to get token from cookie
		cookieKey := utils.GetEnv("COOKIE_TOKEN_KEY", "auth_token")
		cookie, err := c.Cookie(cookieKey)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "No token provided",
			})
			return
		}
		token = cookie
	} else {
		token = strings.Replace(token, "Bearer ", "", 1)

		// Check if token is in blacklist
		exists, _ := middlewares.GetRedis(c).Exists(context.Background(), "token_black_list:"+token).Result()
		if exists == 1 {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Token has been revoked",
			})
			return
		}
	}

	// Parse and validate token
	claims := jwt.MapClaims{}
	parsedToken, err := jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (any, error) {
		return []byte(utils.GetEnv("JWT_SECRET", "")), nil
	})

	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "Invalid token",
		})
		return
	}

	// Check if token was issued before password change
	if username, ok := claims["username"].(string); ok {
		if middlewares.IsTokenRevokedByPasswordChange(c, username, parsedToken) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Token has been revoked due to password change",
			})
			return
		}
	}

	// Get user from database
	db := middlewares.GetDB(c)
	var user models.User
	if err := db.Where("username = ?", claims["username"]).First(&user).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "Invalid token",
		})
		return
	}

	// Generate and return auth data
	authData, err := getUserAuth(db, &user)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    authData,
	})
}

// LoginAsGuest creates or retrieves a guest user session
func LoginAsGuest(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)

	// Get or generate session ID
	sessionID, _ := c.Cookie("sessionId")
	if sessionID == "" {
		sessionID = uuid.New().String()
		c.SetCookie("sessionId", sessionID, 30*24*60*60, "/", "", false, true)
	}

	// Create default password hash
	defaultPassword, err := bcrypt.GenerateFromPassword([]byte("guest"), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Create or get guest role
	var role models.Role
	if err := db.Where(models.Role{Name: "Guest"}).
		Attrs(models.Role{
			Name:        "Guest",
			Permissions: models.JSONArray[models.Permission]{"individual"},
			ID:          "guest",
		}).FirstOrCreate(&role).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Create or update guest user
	var user models.User
	result := db.Where("username = ?", sessionID).First(&user)

	if result.Error != nil {
		// User doesn't exist, create new one
		user = models.User{
			Username: sessionID,
			Name:     "Guest",
			Email:    sessionID + "@nexdor.vn",
			Password: string(defaultPassword),
			Role:     role.ID,
			IsGuest:  true,
			Status:   models.UserStatusActive,
		}

		if err := db.Create(&user).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	} else {
		// User exists, update fields
		user.Name = "Guest"
		user.Email = sessionID + "@nexdor.tech"
		user.Password = string(defaultPassword)
		user.Role = role.ID
		user.IsGuest = true

		if err := db.Save(&user).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	// Track login device
	userAgent := c.Request.Header.Get("User-Agent")
	user.LastLoginDevice = userAgent

	var lastLoginDevices = user.LastLoginDevices
	if !utils.Contains(lastLoginDevices, userAgent) {
		lastLoginDevices = append(lastLoginDevices, userAgent)
		user.LastLoginDevices = lastLoginDevices
		if err := db.Save(&user).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	// Generate authentication data
	authData, err := getUserAuth(db, &user)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Set authentication cookie
	c.SetCookie(utils.GetEnv("COOKIE_TOKEN_KEY", "auth_token"),
		authData.AccessToken,
		30*24*60*60, // 30 days
		"/",
		"",
		utils.GetEnv("ENV", "") == "prod",
		true)

	// Return successful response
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    authData,
	})
}

// Logout removes user session and FCM token
func Logout(c *gin.Context) {
	// Get database and user from middleware
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	if user != nil {
		// Clean up FCM token
		var fcmToken models.FCMToken
		if err := db.Where("user_id = ?", user.ID).First(&fcmToken).Error; err == nil {
			// Unsubscribe from topic
			if err := utils.UnsubscribeFromTopic(fcmToken.FCMToken, "topic_new_order"); err != nil {
				// Log error but continue
				c.Error(err)
			}

			// Delete token
			if err := db.Delete(&fcmToken).Error; err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   err.Error(),
				})
				return
			}
		}
	}

	// Clear auth cookie
	cookieKey := utils.GetEnv("COOKIE_TOKEN_KEY", "auth_token")
	c.SetCookie(cookieKey, "", -1, "/", "", utils.GetEnv("ENV", "") == "prod", true)

	token := c.GetHeader("x-access-token")
	if token != "" {
		middlewares.GetRedis(c).Set(context.Background(), "token_black_list:"+token, "true", 24*time.Hour)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// ActiveAccount activates a pending user account
func ActiveAccount(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)

	// Parse request body
	var request struct {
		ID       string `json:"id" binding:"required"`
		Code     string `json:"code" binding:"required"`
		Password string `json:"password" binding:"omitempty"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get existing user and validate
	var user models.User
	if err := db.First(&user, "id = ?", request.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "user_not_found",
		})
		return
	}

	// Verify user is in pending state
	if user.Status != models.UserStatusPending {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "user_already_activated",
		})
		return
	}

	// Initialize update fields map with status change
	updateFields := map[string]any{
		"status": models.UserStatusActive,
	}

	// Handle password logic
	if request.Password != "" {
		// Use the provided password
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(request.Password), bcrypt.DefaultCost)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "Failed to hash password",
			})
			return
		}
		updateFields["password"] = string(hashedPassword)
	} else {
		// Check if user has an existing password
		if user.Password == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "password_required",
			})
			return
		}
	}

	// Update user record
	if err := db.Model(&models.User{}).Where("id = ?", request.ID).Updates(updateFields).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get updated user
	if err := db.First(&user, "id = ?", request.ID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// getUserAuth generates authentication data for a user
func getUserAuth(db *gorm.DB, user *models.User) (*models.AuthData, error) {
	// Initialize managed resources
	var managedSites []models.Site
	var managedHubs []models.Hub
	var managedBrands []models.Brand

	// Get user permissions from role
	var permissions []models.Permission
	if role, found := models.GetRoleByID(user.Role); found {
		permissions = role.Permissions
	}

	// System-wide permissions would go here
	// if utils.Contains(role.Permissions, "system") {
	// 	db.Select("id", "brand_id", "hub_id", "address", "name").Find(&managedSites)
	// 	db.Select("id", "name").Find(&managedBrands)
	// 	db.Select("id", "name").Find(&managedHubs)
	// } else {
	// 	// Handle role-based access based on selectors
	// 	if utils.Contains(role.Selectors, "brand") {
	// 		db.Where("id IN ?", user.Brands).Find(&managedBrands)
	// 		db.Where("brand_id IN ?", user.Brands).Find(&managedSites)
	// 	}
	// 	// Add other role selector handling...
	// }

	// Generate JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"username": user.Username,
		"exp":      time.Now().Add(time.Hour * 24 * 30).Unix(),
		"iat":      time.Now().Unix(),
	})

	// Sign the token
	tokenString, err := token.SignedString([]byte(utils.GetEnv("JWT_SECRET", "")))
	if err != nil {
		return nil, err
	}

	// Fetch managed sites, hubs, and brands
	// Only query if the arrays are not empty
	if len(user.Sites) > 0 {
		if err := db.Where("id IN ?", []string(user.Sites)).Find(&managedSites).Error; err != nil {
			return nil, fmt.Errorf("failed to fetch managed sites: %w", err)
		}
	}
	if len(user.Hubs) > 0 {
		if err := db.Where("id IN ?", []string(user.Hubs)).Find(&managedHubs).Error; err != nil {
			return nil, fmt.Errorf("failed to fetch managed hubs: %w", err)
		}
	}
	if len(user.Brands) > 0 {
		if err := db.Where("id IN ?", []string(user.Brands)).Find(&managedBrands).Error; err != nil {
			return nil, fmt.Errorf("failed to fetch managed brands: %w", err)
		}
	}

	// Return auth data structure
	return &models.AuthData{
		AccessToken: tokenString,
		User: &models.UserAuth{
			User:          user,
			ManagedSites:  managedSites,
			ManagedHubs:   managedHubs,
			ManagedBrands: managedBrands,
			Permissions:   permissions, // Add the user's permissions to the response
		},
	}, nil
}
