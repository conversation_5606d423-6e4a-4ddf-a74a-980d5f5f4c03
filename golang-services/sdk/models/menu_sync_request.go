package models

import (
	"time"
)

type MenuSyncRequest struct {
	// Base model fields
	ID string `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Basic information
	SiteID      string     `json:"site_id" gorm:"index"`
	MenuData    any        `json:"menu_data" gorm:"type:jsonb"`
	Callback    any        `json:"callback" gorm:"type:jsonb"`
	Status      string     `json:"status" gorm:"type:varchar(20);check:status IN ('pending', 'processing', 'success', 'failed');default:'pending'"`
	StartedAt   *time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`

	// New fields for selective sync
	SyncItems   JSONArray[string] `json:"sync_items" gorm:"type:jsonb"`
	DeleteItems JSONArray[string] `json:"delete_items" gorm:"type:jsonb"`
}

// MenuSyncLog represents detailed logging for menu sync operations
type MenuSyncLog struct {
	// Base model fields
	ID string `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Reference to sync request
	SyncRequestID string `json:"sync_request_id" gorm:"index"`
	SiteID        string `json:"site_id" gorm:"index"`

	// Sync operation details
	Platform      string            `json:"platform" gorm:"index"` // grab, shopee, be, etc.
	Status        string            `json:"status" gorm:"type:varchar(20);check:status IN ('success', 'failed')"`
	ErrorMsg      string            `json:"error_msg"`
	RequestData   any               `json:"request_data" gorm:"type:jsonb"`
	ResponseData  any               `json:"response_data" gorm:"type:jsonb"`
	SyncItems     JSONArray[string] `json:"sync_items" gorm:"type:jsonb"`
	DeleteItems   JSONArray[string] `json:"delete_items" gorm:"type:jsonb"`
	CompletedTime *time.Time        `json:"completed_time"`
}

// TableName specifies the table name for the MenuSyncRequest model
func (MenuSyncRequest) TableName() string {
	return "menu_sync_requests"
}

// TableName specifies the table name for the MenuSyncLog model
func (MenuSyncLog) TableName() string {
	return "menu_sync_logs"
}
