package models

import (
	"time"
)

// Permission represents a type-safe permission string
type Permission string

// Permission constants organized by functional areas
const (
	// Brand management permissions
	PermViewBrands  Permission = "brand:view"
	PermCreateBrand Permission = "brand:create"
	PermUpdateBrand Permission = "brand:update"
	PermDeleteBrand Permission = "brand:delete"

	// Hub management permissions
	PermViewHubs       Permission = "hub:view"
	PermCreateHub      Permission = "hub:create"
	PermUpdateHub      Permission = "hub:update"
	PermDeleteHub      Permission = "hub:delete"
	PermManageHubStaff Permission = "hub:manage_staff"

	// Product management permissions
	PermViewProducts  Permission = "product:view"
	PermCreateProduct Permission = "product:create"
	PermUpdateProduct Permission = "product:update"
	PermDeleteProduct Permission = "product:delete"

	// Order management permissions
	PermViewOrders     Permission = "order:view"
	PermCreateOrder    Permission = "order:create"
	PermUpdateOrder    Permission = "order:update"
	PermDeleteOrder    Permission = "order:delete"
	PermProcessPayment Permission = "order:process_payment"

	// User management permissions
	PermViewUsers   Permission = "user:view"
	PermCreateUser  Permission = "user:create"
	PermUpdateUser  Permission = "user:update"
	PermDeleteUser  Permission = "user:delete"
	PermAssignRoles Permission = "user:assign_roles"

	// Reporting permissions
	PermViewReports   Permission = "report:view"
	PermCreateReports Permission = "report:create"
	PermExportReports Permission = "report:export"

	// Settings permissions
	PermViewSettings   Permission = "settings:view"
	PermUpdateSettings Permission = "settings:update"

	// Menu management permissions
	PermManageMenu Permission = "menu:manage"

	// Site management permissions
	PermViewSites  Permission = "site:view"
	PermCreateSite Permission = "site:create"
	PermUpdateSite Permission = "site:update"
	PermDeleteSite Permission = "site:delete"

	// Bill management permissions
	PermViewBills  Permission = "bill:view"
	PermCreateBill Permission = "bill:create"
	PermUpdateBill Permission = "bill:update"
	PermDeleteBill Permission = "bill:delete"

	// Integration permissions
	PermManageIntegration Permission = "integration:manage"

	// Subscription permissions
	PermManageSubscription Permission = "subscription:manage"
)

// PermissionGroup represents a logical grouping of permissions
type PermissionGroup struct {
	Name        string       // Name of the permission group
	Permissions []Permission // List of permissions in this group
}

// AllPermissionGroups contains all permission groups defined in the system
var AllPermissionGroups = []PermissionGroup{
	{
		Name: "Quản lý subscription",
		Permissions: []Permission{
			PermManageSubscription,
		},
	},
	{
		Name: "Quản lý thương hiệu",
		Permissions: []Permission{
			PermViewBrands,
			PermCreateBrand,
			PermUpdateBrand,
			PermDeleteBrand,
		},
	},
	{
		Name: "Quản lý điểm bán",
		Permissions: []Permission{
			PermViewHubs,
			PermCreateHub,
			PermUpdateHub,
			PermDeleteHub,
			PermManageHubStaff,
		},
	},
	{
		Name: "Quản lý sản phẩm",
		Permissions: []Permission{
			PermViewProducts,
			PermCreateProduct,
			PermUpdateProduct,
			PermDeleteProduct,
		},
	},
	{
		Name: "Quản lý đơn hàng",
		Permissions: []Permission{
			PermViewOrders,
			PermCreateOrder,
			PermUpdateOrder,
			PermDeleteOrder,
			PermProcessPayment,
		},
	},
	{
		Name: "Quản lý người dùng",
		Permissions: []Permission{
			PermViewUsers,
			PermCreateUser,
			PermUpdateUser,
			PermDeleteUser,
			PermAssignRoles,
		},
	},
	{
		Name: "Báo cáo",
		Permissions: []Permission{
			PermViewReports,
			PermCreateReports,
			PermExportReports,
		},
	},
	{
		Name: "Cài đặt hệ thống",
		Permissions: []Permission{
			PermViewSettings,
			PermUpdateSettings,
		},
	},
}

// String returns the string representation of the Permission
func (p Permission) String() string {
	return string(p)
}

// GetAllPermissions returns a flat list of all permissions
func GetAllPermissions() []Permission {
	var allPerms []Permission
	for _, group := range AllPermissionGroups {
		allPerms = append(allPerms, group.Permissions...)
	}
	return allPerms
}

// RoleID represents a unique identifier for roles
type RoleID string

// Standard role ID constants
const (
	OwnerRoleID        RoleID = "owner"         // Chủ sở hữu
	BrandManagerRoleID RoleID = "brand_manager" // Quản lý thương hiệu
	StoreManagerRoleID RoleID = "store_manager" // Quản lý cửa hàng
	CashierRoleID      RoleID = "cashier"       // Thu ngân
)

// Role defines the structure for role definitions with associated permissions
type Role struct {
	// Basic properties of a role
	Name        string                `json:"name"`
	ID          string                `json:"code"` // Using ID instead of Code for consistency
	Permissions JSONArray[Permission] `json:"permissions"`
	// Metadata for tracking
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Predefined roles with their respective permissions
var (
	// OwnerRoleDef defines the owner role with full system access
	OwnerRoleDef = Role{
		Name: "Chủ sở hữu",
		ID:   "owner",
		// Owner has all permissions in the system
		Permissions: JSONArray[Permission]{
			// Brand management - full access
			PermViewBrands, PermCreateBrand, PermUpdateBrand, PermDeleteBrand,
			// Hub management - full access
			PermViewHubs, PermCreateHub, PermUpdateHub, PermDeleteHub, PermManageHubStaff,
			// Product management - full access
			PermViewProducts, PermCreateProduct, PermUpdateProduct, PermDeleteProduct,
			// Order management - full access
			PermViewOrders, PermCreateOrder, PermUpdateOrder, PermDeleteOrder, PermProcessPayment,
			// User management - full access
			PermViewUsers, PermCreateUser, PermUpdateUser, PermDeleteUser, PermAssignRoles,
			// Reporting - full access
			PermViewReports, PermCreateReports, PermExportReports,
			// Settings - full access
			PermViewSettings, PermUpdateSettings,
			// Menu management
			PermManageMenu,
			// Site management
			PermViewSites, PermCreateSite, PermUpdateSite, PermDeleteSite,
			// Bill management
			PermViewBills, PermCreateBill, PermUpdateBill, PermDeleteBill,
			// Integration management
			PermManageIntegration,
			// Subscription management
			PermManageSubscription,
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// BrandManagerRoleDef defines the brand manager role
	BrandManagerRoleDef = Role{
		Name: "Quản lý thương hiệu",
		ID:   "brand_manager",
		// Brand Manager has permissions related to brands, hubs, products, and reports
		Permissions: JSONArray[Permission]{
			// Brand management - full access
			PermViewBrands, PermCreateBrand, PermUpdateBrand, PermDeleteBrand,
			// Hub management - view only plus create/update
			PermViewHubs, PermCreateHub, PermUpdateHub,
			// Product management - full access
			PermViewProducts, PermCreateProduct, PermUpdateProduct, PermDeleteProduct,
			// Order management - view only
			PermViewOrders,
			// User management - limited
			PermViewUsers, PermCreateUser, PermUpdateUser,
			// Reporting - view and export
			PermViewReports, PermExportReports,
			// Settings - view only
			PermViewSettings,
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// StoreManagerRoleDef defines the hub manager role
	StoreManagerRoleDef = Role{
		Name: "Quản lý điểm bán",
		ID:   "store_manager",
		// Hub Manager has permissions related to their hub, products, staff, and orders
		Permissions: JSONArray[Permission]{
			// Brand management - view only
			PermViewBrands,
			// Hub management - view and update only for assigned hubs
			PermViewHubs, PermUpdateHub, PermManageHubStaff,
			// Product management - full access for hub products
			PermViewProducts, PermCreateProduct, PermUpdateProduct, PermDeleteProduct,
			// Order management - full access
			PermViewOrders, PermCreateOrder, PermUpdateOrder, PermDeleteOrder, PermProcessPayment,
			// User management - limited to staff
			PermViewUsers, PermCreateUser, PermUpdateUser,
			// Reporting - view and create
			PermViewReports, PermCreateReports, PermExportReports,
			// Settings - view only
			PermViewSettings,
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// CashierRoleDef defines the cashier role
	CashierRoleDef = Role{
		Name: "Thu ngân",
		ID:   "cashier",
		// Cashier has limited permissions focused on orders and products
		Permissions: JSONArray[Permission]{
			// Product management - view only
			PermViewProducts,
			// Order management - create, view, and process payment
			PermViewOrders, PermCreateOrder, PermProcessPayment,
			// Reporting - basic view
			PermViewReports,
			PermViewHubs,   // View hubs to know where they are working
			PermViewBrands, // View brands to know which brand they are working for
			PermViewSites,  // View sites to know where they are working
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
)

// GetRoleByID returns a role by its id
func GetRoleByID(id string) (*Role, bool) {
	switch id {
	case "owner":
		return &OwnerRoleDef, true
	case "brand_manager":
		return &BrandManagerRoleDef, true
	case "store_manager":
		return &StoreManagerRoleDef, true
	case "cashier":
		return &CashierRoleDef, true
	default:
		return nil, false
	}
}

// GetAllRoles returns all predefined roles
func GetAllRoles() []Role {
	return []Role{
		OwnerRoleDef,
		BrandManagerRoleDef,
		StoreManagerRoleDef,
		CashierRoleDef,
	}
}
