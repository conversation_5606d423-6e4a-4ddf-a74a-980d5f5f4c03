package merchant

import (
	"net/http"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/be"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/grab"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/lazada"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/nexpos"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/nutifood"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/shopee"
	shopee_ecom "github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/shopee-ecom"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"gorm.io/gorm"
)

// MerchantRequest is a shared HTTP client for merchant API requests
var MerchantRequest = &http.Client{
	Timeout: 30 * time.Second,
}

// IMerchant defines the interface for merchant platform interactions
type IMerchant interface {
	// GetToken authenticates and retrieves a token
	GetToken(token *models.Token) error

	// GetStoreListByAuth authenticates and retrieves a list of stores
	GetStoreListByAuth(token *models.Token) ([]models.StoreItem, error)

	// GetStore retrieves store information
	GetStore(token *models.Token) (*models.StoreDetail, error)

	// GetStoreList retrieves the list of stores for a merchant
	GetStoreList(token *models.Token) ([]any, error)

	// GetOrderListV2 retrieves the list of orders
	GetOrderListV2(auth *models.Token) (map[string][]models.MerchantOrder, error)

	// GetOrderListByDuration retrieves orders within a specific time range
	GetOrderListByDuration(auth *models.Token, startTime, endTime time.Time) (map[string][]models.MerchantOrder, error)

	// ConfirmOrder confirms an order
	ConfirmOrder(auth *models.Token, orderID string) error

	// CancelOrder cancels an order
	CancelOrder(auth *models.Token, orderID string, cancelType string) error

	// GetOrderDetail retrieves details of a specific order
	GetOrderDetail(auth *models.Token, orderID string) (any, error)

	// UpdateStoreStatus updates the store's open/close status
	UpdateStoreStatus(auth *models.Token, status string, duration int) error

	// GetOpenStatus checks if the store is currently open
	GetOpenStatus(auth *models.Token) (bool, error)

	// GetOpeningHour retrieves the store's opening hours
	GetOpeningHour(auth *models.Token) (any, error)

	// UpdateOpeningHour updates the store's opening hours
	UpdateOpeningHour(auth *models.Token, workingHours any) error

	// GetOrderFeedbacks retrieves order feedbacks from the merchant platform
	GetOrderFeedbacks(auth *models.Token, limit int) ([]any, error)
}

// NewMerchant creates a new merchant client based on the provided source
func NewMerchant(source string) IMerchant {
	switch source {
	case "grab", "grab_mart":
		return grab.NewGrabMerchantClient()
	case "shopee", "shopee_food", "shopee_fresh":
		return shopee.NewShopeeMerchantClient()
	case "shopee_ecom":
		return shopee_ecom.NewShopeeEcomClient()
	case "be", "be_mart":
		return be.NewBEMerchantClient()
	case "lazada":
		return lazada.NewLazadaClient()
	default:
		return nil
	}
}

// NewNutifoodClient creates a new client for Nutifood integration
func NewNutifoodClient(baseURL, apiKey string, db *gorm.DB) *nutifood.Client {
	return nutifood.NewClient(baseURL, apiKey, db)
}

// NewNexposClient creates a new client for Nexpos integration
func NewNexposClient(db *gorm.DB) *nexpos.Client {
	return nexpos.NewClient(db)
}
