// Package lazada implements the Lazada merchant client
package lazada

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/lazada/iop"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/tidwall/gjson"
)

const (
	// API credentials
	apiKey      = "133591"
	apiSecret   = "obpO7s3ZZqsXgrOc3BZnrgj8BDKaecLJ"
	region      = "VN"
	callbackURL = "https://api.nexpos.io/callback/lazada"
)

// LazadaClient implements the IMerchant interface for Lazada
type LazadaClient struct {
	client *iop.IopClient
}

// NewLazadaClient creates a new Lazada client
func NewLazadaClient() *LazadaClient {
	clientOptions := &iop.ClientOptions{
		APIKey:    apiKey,
		APISecret: apiSecret,
		Region:    region,
	}

	return &LazadaClient{
		client: iop.NewClient(clientOptions),
	}
}

// GetToken authenticates and retrieves a token
func (c *LazadaClient) GetToken(token *models.Token) error {
	if token.RefreshToken != "" {
		// If refresh token exists, use it to get a new access token
		c.client.AddAPIParam("refresh_token", token.RefreshToken)
		resp, err := c.client.Execute("/auth/token/refresh", "POST", nil)
		if err != nil {
			return fmt.Errorf("error refreshing token: %w", err)
		}

		// Parse the response to get the tokens
		if resp.Code != "0" {
			return fmt.Errorf("error response from Lazada: %s", resp.Message)
		}

		jsonData := gjson.ParseBytes(resp.Data)
		token.AccessToken = jsonData.Get("access_token").String()
		token.RefreshToken = jsonData.Get("refresh_token").String()

		// Store additional parameters if needed
		if token.Params == nil {
			token.Params = make(map[string]string)
		}
		token.Params["expires_in"] = jsonData.Get("expires_in").String()

		return nil
	} else if code, ok := token.Params["code"]; ok && code != "" {
		// Exchange authorization code for token
		c.client.AddAPIParam("code", code)
		resp, err := c.client.Execute("/auth/token/create", "POST", nil)
		if err != nil {
			return fmt.Errorf("error creating token: %w", err)
		}

		// Parse the response
		if resp.Code != "0" {
			return fmt.Errorf("error response from Lazada: %s", resp.Message)
		}

		jsonData := gjson.ParseBytes(resp.Data)
		token.AccessToken = jsonData.Get("access_token").String()
		token.RefreshToken = jsonData.Get("refresh_token").String()

		// Store additional parameters
		if token.Params == nil {
			token.Params = make(map[string]string)
		}
		token.Params["expires_in"] = jsonData.Get("expires_in").String()

		return nil
	}

	return fmt.Errorf("either refresh_token or code in params is required for authentication")
}

// GetStoreListByAuth authenticates and retrieves a list of stores
func (c *LazadaClient) GetStoreListByAuth(token *models.Token) ([]models.StoreItem, error) {
	// Lazada typically has one store per seller, so we'll get the seller info
	c.client.SetAccessToken(token.AccessToken)
	resp, err := c.client.Execute("/seller/get", "GET", nil)
	if err != nil {
		return nil, fmt.Errorf("error getting seller info: %w", err)
	}

	// Parse response
	if resp.Code != "0" {
		return nil, fmt.Errorf("error response from Lazada: %s", resp.Message)
	}

	// Extract store data
	storeData := gjson.ParseBytes(resp.Data)
	if !storeData.Exists() {
		return nil, fmt.Errorf("no store data found in response")
	}

	// Create store item
	storeItem := models.StoreItem{
		AccessToken: token.AccessToken,
		StoreID:     storeData.Get("seller_id").String(),
		StoreName:   storeData.Get("name").String(),
		StoreType:   "LAZADA", // Use a consistent store type
	}

	return []models.StoreItem{storeItem}, nil
}

// GetStore retrieves store information
func (c *LazadaClient) GetStore(token *models.Token) (*models.StoreDetail, error) {
	c.client.SetAccessToken(token.AccessToken)
	resp, err := c.client.Execute("/seller/get", "GET", nil)
	if err != nil {
		return nil, fmt.Errorf("error getting store details: %w", err)
	}

	// Parse response
	if resp.Code != "0" {
		return nil, fmt.Errorf("error response from Lazada: %s", resp.Message)
	}

	// Extract store data
	storeData := gjson.ParseBytes(resp.Data)
	if !storeData.Exists() {
		return nil, fmt.Errorf("no store data found in response")
	}

	// Create store detail
	var rawData interface{}
	if err := json.Unmarshal(resp.Data, &rawData); err != nil {
		return nil, fmt.Errorf("error unmarshaling store data: %w", err)
	}

	storeDetail := &models.StoreDetail{
		ID:      storeData.Get("seller_id").String(),
		Name:    storeData.Get("name").String(),
		Phone:   storeData.Get("phone_number").String(),
		Address: storeData.Get("address").String(),
		Raw:     rawData,
	}

	return storeDetail, nil
}

// GetStoreList retrieves the list of stores for a merchant
// Same as GetStoreListByAuth for Lazada as there's typically one store per seller
func (c *LazadaClient) GetStoreList(token *models.Token) ([]any, error) {
	storeItems, err := c.GetStoreListByAuth(token)
	if err != nil {
		return nil, err
	}

	// Convert to []any
	result := make([]any, len(storeItems))
	for i, item := range storeItems {
		itemMap := map[string]interface{}{
			"access_token": item.AccessToken,
			"store_id":     item.StoreID,
			"store_name":   item.StoreName,
			"store_type":   item.StoreType,
		}
		result[i] = itemMap
	}

	return result, nil
}

// GetOrderListV2 retrieves the list of orders
func (c *LazadaClient) GetOrderListV2(auth *models.Token) (map[string][]models.MerchantOrder, error) {
	// Get orders from the last 24 hours by default
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -1) // 1 day ago

	return c.GetOrderListByDuration(auth, startTime, endTime)
}

// GetOrderListByDuration retrieves orders within a specific time range
func (c *LazadaClient) GetOrderListByDuration(auth *models.Token, startTime, endTime time.Time) (map[string][]models.MerchantOrder, error) {
	c.client.SetAccessToken(auth.AccessToken)

	// Format times in ISO 8601 format as required by Lazada API
	createdAfter := startTime.Format(time.RFC3339)
	createdBefore := endTime.Format(time.RFC3339)

	c.client.AddAPIParam("created_after", createdAfter)
	c.client.AddAPIParam("created_before", createdBefore)
	c.client.AddAPIParam("status", "all")
	c.client.AddAPIParam("limit", "100") // Adjust as needed

	resp, err := c.client.Execute("/orders/get", "GET", nil)
	if err != nil {
		return nil, fmt.Errorf("error getting orders: %w", err)
	}

	// Parse response
	if resp.Code != "0" {
		return nil, fmt.Errorf("error response from Lazada: %s", resp.Message)
	}

	// Extract orders
	jsonData := gjson.ParseBytes(resp.Data)
	orders := jsonData.Get("orders").Array()

	result := make(map[string][]models.MerchantOrder)
	var merchantOrders []models.MerchantOrder

	for _, order := range orders {
		// Parse creation and update dates
		createdAt, _ := time.Parse(time.RFC3339, order.Get("created_at").String())
		updatedAt, _ := time.Parse(time.RFC3339, order.Get("updated_at").String())

		// Map Lazada status to standard status
		status := mapLazadaStatus(order.Get("status").String())

		// Extract order data as raw JSON
		var orderData interface{}
		if err := json.Unmarshal([]byte(order.Raw), &orderData); err != nil {
			return nil, fmt.Errorf("error unmarshaling order data: %w", err)
		}

		// Create merchant order
		merchantOrder := models.MerchantOrder{
			Source:       "lazada",
			LongOrderID:  order.Get("order_id").String(),
			ShortOrderID: order.Get("order_number").String(),
			Status:       status,
			CreatedAt:    createdAt,
			UpdatedAt:    updatedAt,
			DataInList:   orderData,
		}

		// Generate MD5 hash for comparing changes
		orderJSON, _ := json.Marshal(orderData)
		merchantOrder.MD5 = fmt.Sprintf("%x", orderJSON)

		merchantOrders = append(merchantOrders, merchantOrder)
	}

	result["orders"] = merchantOrders
	return result, nil
}

// mapLazadaStatus maps Lazada order status to standard status
func mapLazadaStatus(status string) string {
	// Lazada statuses: unpaid, pending, canceled, ready_to_ship, delivered, returned, shipped, failed
	switch status {
	case "unpaid", "pending":
		return "PENDING"
	case "ready_to_ship", "shipped":
		return "DOING"
	case "delivered":
		return "FINISH"
	case "canceled", "returned", "failed":
		return "CANCEL"
	default:
		return "PENDING"
	}
}

// ConfirmOrder confirms an order
func (c *LazadaClient) ConfirmOrder(auth *models.Token, orderID string) error {
	c.client.SetAccessToken(auth.AccessToken)
	c.client.AddAPIParam("order_item_id", orderID)

	resp, err := c.client.Execute("/order/pack", "POST", nil)
	if err != nil {
		return fmt.Errorf("error confirming order: %w", err)
	}

	// Parse response
	if resp.Code != "0" {
		return fmt.Errorf("error response from Lazada: %s", resp.Message)
	}

	return nil
}

// CancelOrder cancels an order
func (c *LazadaClient) CancelOrder(auth *models.Token, orderID string, cancelType string) error {
	c.client.SetAccessToken(auth.AccessToken)

	// Define cancel reason based on cancelType
	var reason string
	switch cancelType {
	case "OUT_OF_STOCK":
		reason = "Out of stock"
	case "CUSTOMER_REQUEST":
		reason = "Customer request"
	default:
		reason = "Unable to fulfill"
	}

	c.client.AddAPIParam("order_item_id", orderID)
	c.client.AddAPIParam("reason", reason)

	resp, err := c.client.Execute("/order/cancel", "POST", nil)
	if err != nil {
		return fmt.Errorf("error canceling order: %w", err)
	}

	// Parse response
	if resp.Code != "0" {
		return fmt.Errorf("error response from Lazada: %s", resp.Message)
	}

	return nil
}

// GetOrderDetail retrieves details of a specific order
func (c *LazadaClient) GetOrderDetail(auth *models.Token, orderID string) (any, error) {
	c.client.SetAccessToken(auth.AccessToken)
	c.client.AddAPIParam("order_id", orderID)

	resp, err := c.client.Execute("/order/get", "GET", nil)
	if err != nil {
		return nil, fmt.Errorf("error getting order details: %w", err)
	}

	// Parse response
	if resp.Code != "0" {
		return nil, fmt.Errorf("error response from Lazada: %s", resp.Message)
	}

	// Extract order data
	var orderDetail interface{}
	if err := json.Unmarshal(resp.Data, &orderDetail); err != nil {
		return nil, fmt.Errorf("error unmarshaling order data: %w", err)
	}

	return orderDetail, nil
}

// UpdateStoreStatus updates the store's open/close status
func (c *LazadaClient) UpdateStoreStatus(auth *models.Token, status string, duration int) error {
	// Lazada doesn't have a direct API to update store status
	return fmt.Errorf("updating store status is not supported by Lazada API")
}

// GetOpenStatus checks if the store is currently open
func (c *LazadaClient) GetOpenStatus(auth *models.Token) (bool, error) {
	// Lazada doesn't have a direct API to check store open status
	// We can assume the store is always open
	return true, nil
}

// GetOpeningHour retrieves the store's opening hours
func (c *LazadaClient) GetOpeningHour(auth *models.Token) (any, error) {
	// Lazada doesn't provide opening hours through API
	// Return a default schedule
	defaultHours := map[string]interface{}{
		"monday":    map[string]string{"open": "09:00", "close": "18:00"},
		"tuesday":   map[string]string{"open": "09:00", "close": "18:00"},
		"wednesday": map[string]string{"open": "09:00", "close": "18:00"},
		"thursday":  map[string]string{"open": "09:00", "close": "18:00"},
		"friday":    map[string]string{"open": "09:00", "close": "18:00"},
		"saturday":  map[string]string{"open": "09:00", "close": "18:00"},
		"sunday":    map[string]string{"open": "09:00", "close": "18:00"},
	}

	return defaultHours, nil
}

// UpdateOpeningHour updates the store's opening hours
func (c *LazadaClient) UpdateOpeningHour(auth *models.Token, workingHours any) error {
	// Lazada doesn't have an API to update store opening hours
	return fmt.Errorf("updating opening hours is not supported by Lazada API")
}

// GetOrderFeedbacks retrieves order feedbacks from the merchant platform
func (c *LazadaClient) GetOrderFeedbacks(auth *models.Token, limit int) ([]any, error) {
	c.client.SetAccessToken(auth.AccessToken)
	c.client.AddAPIParam("limit", strconv.Itoa(limit))

	resp, err := c.client.Execute("/review/get", "GET", nil)
	if err != nil {
		return nil, fmt.Errorf("error getting order feedbacks: %w", err)
	}

	// Parse response
	if resp.Code != "0" {
		return nil, fmt.Errorf("error response from Lazada: %s", resp.Message)
	}

	// Extract feedback data
	jsonData := gjson.ParseBytes(resp.Data)
	feedbacks := jsonData.Get("reviews").Array()
	if len(feedbacks) == 0 {
		return []any{}, nil
	}

	// Process feedbacks
	result := make([]any, len(feedbacks))
	for i, feedback := range feedbacks {
		var feedbackData interface{}
		if err := json.Unmarshal([]byte(feedback.Raw), &feedbackData); err != nil {
			return nil, fmt.Errorf("error unmarshaling feedback data: %w", err)
		}

		result[i] = feedbackData
	}

	return result, nil
}
